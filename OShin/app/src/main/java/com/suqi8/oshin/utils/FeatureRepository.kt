package com.suqi8.oshin.utils

import android.content.Context
import com.suqi8.oshin.R


// 定义数据模型
data class item(
    val title: String,
    val summary: String? = null,
    val category: String
)

// 使用 object 创建一个单例的数据仓库
object FeatureRepository {

    private var cachedFeatures: List<item>? = null

    // 提供一个挂起函数来获取数据，内部处理缓存
    suspend fun getFeatures(context: Context): List<item> {
        return cachedFeatures ?: loadFeatures(context).also {
            cachedFeatures = it
        }
    }

    // 将您的巨大列表创建逻辑封装在这里
    private fun loadFeatures(context: Context): List<item> {
        // 这里的 context 建议使用 applicationContext 来避免内存泄漏
        val appContext = context.applicationContext
        return listOf(
            item(title = context.getString(R.string.downgr),
                summary = context.getString(R.string.downgr_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.authcreak),
                summary = context.getString(R.string.authcreak_summary),
                category = "android\\package_manager_services"),
            item(
                title = context.getString(R.string.digestCreak),
                summary = context.getString(R.string.digestCreak_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.UsePreSig),
                summary = context.getString(R.string.UsePreSig_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.enhancedMode),
                summary = context.getString(R.string.enhancedMode_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.bypassBlock),
                summary = context.getString(R.string.bypassBlock_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.shared_user_title),
                summary = context.getString(R.string.shared_user_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.disable_verification_agent_title),
                summary = context.getString(R.string.disable_verification_agent_summary),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.package_manager_services),
                category = "android\\package_manager_services"),
            item(title = context.getString(R.string.oplus_system_services),
                category = "android\\oplus_system_services"),
            item(title = context.getString(R.string.oplus_root_check),
                summary = context.getString(R.string.oplus_root_check_summary),
                category = "android\\oplus_system_services"),
            item(title = context.getString(R.string.desktop_icon_and_text_size_multiplier),
                summary = context.getString(R.string.icon_size_limit_note),
                category = "launcher"),
            item(title = context.getString(R.string.power_consumption_indicator),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.dual_cell),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.absolute_value),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.bold_text),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.alignment),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.update_time),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.font_size),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.dual_row_title),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.first_line_content),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.second_line_content),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.power),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.current),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.voltage),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.temperature_indicator),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.show_cpu_temp_data),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.change_cpu_temp_source),
                summary = context.getString(R.string.enter_thermal_zone_number),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.bold_text),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.alignment),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.update_time),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.font_size),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.dual_row_title),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.first_line_content),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.second_line_content),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.battery_temperature),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.cpu_temperature),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.status_bar_clock),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.hardware_indicator),
                category = "systemui\\hardware_indicator"),
            item(title = context.getString(R.string.status_bar_icon),
                category = "systemui\\statusbar_icon"),
            item(title = context.getString(R.string.hide_status_bar),
                category = "systemui"),
            item(title = context.getString(R.string.enable_all_day_screen_off),
                category = "systemui"),
            item(title = context.getString(R.string.force_trigger_ltpo),
                category = "systemui"),
            item(title = context.getString(R.string.status_bar_clock),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_style),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_size),
                summary = context.getString(R.string.clock_size_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_update_time_title),
                summary = context.getString(R.string.clock_update_time_summary),
                category = "systemui\\status_bar_clock"),
            item(title = "dp To px",
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_top_margin),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_bottom_margin),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_left_margin),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_right_margin),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_years_title),
                summary = context.getString(R.string.show_years_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_month_title),
                summary = context.getString(R.string.show_month_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_day_title),
                summary = context.getString(R.string.show_day_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_week_title),
                summary = context.getString(R.string.show_week_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_cn_hour_title),
                summary = context.getString(R.string.show_cn_hour_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.showtime_period_title),
                summary = context.getString(R.string.showtime_period_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_seconds_title),
                summary = context.getString(R.string.show_seconds_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.show_millisecond_title),
                summary = context.getString(R.string.show_millisecond_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.hide_space_title),
                summary = context.getString(R.string.hide_space_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.dual_row_title),
                summary = context.getString(R.string.dual_row_summary),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.alignment),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_format),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.clock_format_example),
                category = "systemui\\status_bar_clock"),
            item(title = context.getString(R.string.status_bar_icon),
                category = "systemui\\statusbar_icon"),
            item(title = context.getString(R.string.wifi_icon),
                category = "systemui\\statusbar_icon"),
            item(title = context.getString(R.string.wifi_arrow),
                category = "systemui\\statusbar_icon"),
            item(title = context.getString(R.string.force_display_memory),
                category = "launcher\\recent_task"),
            item(title = context.getString(R.string.recent_tasks),
                category = "launcher\\recent_task"),
            item(title = context.getString(R.string.status_bar_notification),
                category = "systemui\\notification"),
            item(title = context.getString(R.string.remove_developer_options_notification),
                summary = context.getString(R.string.notification_restriction_message),
                category = "systemui\\notification"),
            item(title = context.getString(R.string.low_battery_fluid_cloud_off),
                category = "battery"),
            item(title = context.getString(R.string.remove_and_do_not_disturb_notification),
                summary = context.getString(R.string.notification_restriction_message),
                category = "systemui\\notification"),
            item(title = context.getString(R.string.force_enable_xiaobu_call),
                category = "speechassist"),
            item(title = context.getString(R.string.remove_full_screen_translation_restriction),
                category = "ocrscanner"),
            item(title = context.getString(R.string.enable_ultra_combo),
                category = "games"),
            item(title = context.getString(R.string.enable_hok_ai_v1),
                category = "games"),
            item(title = context.getString(R.string.enable_hok_ai_v2),
                summary = context.getString(R.string.realme_gt7pro_feature_unlock_device_restriction),
                category = "games"),
            item(title = context.getString(R.string.enable_hok_ai_v3),
                category = "games"),
            item(title = context.getString(R.string.feature_disable_cloud_control),
                category = "games"),
            item(title = context.getString(R.string.remove_package_restriction),
                category = "games"),
            item(title = context.getString(R.string.enable_all_features),
                summary = context.getString(R.string.enable_all_features_warning),
                category = "games"),
            item(title = context.getString(R.string.enable_pubg_ai),
                category = "games"),
            item(title = context.getString(R.string.auto_start_max_limit),
                summary = context.getString(R.string.auto_start_default_hint),
                category = "battery"),
            item(title = context.getString(R.string.split_screen_multi_window),
                category = "android\\split_screen_multi_window"),
            item(title = context.getString(R.string.remove_all_small_window_restrictions),
                category = "android\\split_screen_multi_window",
            ),
            item(title = context.getString(R.string.force_multi_window_mode),
                category = "android\\split_screen_multi_window"),
            item(title = context.getString(R.string.max_simultaneous_small_windows),
                category = "android\\split_screen_multi_window",
                summary = context.getString(R.string.default_value_hint_negative_one)),
            item(title = context.getString(R.string.small_window_corner_radius),
                category = "android\\split_screen_multi_window",
                summary = context.getString(R.string.default_value_hint_negative_one)),
            item(title = context.getString(R.string.small_window_focused_shadow),
                category = "android\\split_screen_multi_window",
                summary = context.getString(R.string.default_value_hint_negative_one)),
            item(title = context.getString(R.string.small_window_unfocused_shadow),
                category = "android\\split_screen_multi_window",
                summary = context.getString(R.string.default_value_hint_negative_one)),
            item(title = context.getString(R.string.custom_display_model),
                summary = context.getString(R.string.hint_empty_content_default),
                category = "settings"),
            item(title = context.getString(R.string.remove_swipe_page_ads),
                summary = context.getString(R.string.clear_wallet_data_notice),
                category = "wallet"),
            item(title = context.getString(R.string.enable_ota_card_bg),
                category = "settings"),
            item(title = context.getString(R.string.select_background_btn),
                category = "settings"),
            item(title = context.getString(R.string.corner_radius_title),
                category = "settings"),
            item(title = context.getString(R.string.force_enable_fold_mode),
                category = "launcher"),
            item(title = context.getString(R.string.fold_mode),
                category = "launcher"),
            item(title = context.getString(R.string.force_enable_fold_dock),
                category = "launcher"),
            item(title = context.getString(R.string.adjust_dock_transparency),
                category = "launcher"),
            item(title = context.getString(R.string.force_enable_dock_blur),
                summary = context.getString(R.string.force_enable_dock_blur_undevice),
                category = "launcher"),
            item(title = context.getString(R.string.remove_game_filter_root_detection),
                category = "games"),
            item(title = context.getString(R.string.remove_all_popup_delays),
                summary = context.getString(R.string.remove_all_popup_delays_eg),
                category = "phonemanager"),
            item(title = context.getString(R.string.remove_all_popup_delays),
                summary = context.getString(R.string.remove_all_popup_delays_eg),
                category = "oplusphonemanager"),
            item(title = context.getString(R.string.remove_message_ads),
                category = "mms"),
            item(title = context.getString(R.string.force_show_nfc_security_chip),
                category = "settings"),
            item(title = context.getString(R.string.security_payment_remove_risky_fluid_cloud),
                category = "securepay"),
            item(title = context.getString(R.string.custom_score),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "phonemanager"),
            item(title = context.getString(R.string.custom_prompt_content),
                category = "phonemanager"),
            item(title = context.getString(R.string.custom_animation_duration),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "phonemanager"),
            item(title = context.getString(R.string.custom_score),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "oplusphonemanager"),
            item(title = context.getString(R.string.custom_prompt_content),
                category = "oplusphonemanager"),
            item(title = context.getString(R.string.custom_animation_duration),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "oplusphonemanager"),
            item(title = context.getString(R.string.feature),
                category = "settings\\feature"),
            item(title = context.getString(R.string.force_enable_all_features),summary = context.getString(R.string.enable_all_features_warning),category = "settings\\feature"),
            item(title = context.getString(R.string.demo_only_device),category = "settings\\feature"),
            item(title = context.getString(R.string.retail_locked_terminal),category = "settings\\feature"),
            item(title = context.getString(R.string.force_enable_karaoke),category = "settings\\feature"),
            item(title = context.getString(R.string.force_enable_3d_camera_color),category = "settings\\feature"),
            item(title = context.getString(R.string.force_aon_explorer),category = "settings\\feature"),
            item(title = context.getString(R.string.force_enable_app_freeze),category = "settings\\feature"),
            item(title = context.getString(R.string.check_ble_audio_whitelist),category = "settings\\feature"),
            item(title = context.getString(R.string.force_breathing_light_sync),category = "settings\\feature"),
            item(title = context.getString(R.string.force_breathing_light_color),category = "settings\\feature"),
            item(title = context.getString(R.string.force_support_wide_gamut),category = "settings\\feature"),
            item(title = context.getString(R.string.force_support_color_mode),category = "settings\\feature"),
            item(title = context.getString(R.string.force_support_hidden_app_feature),category = "settings\\feature"),
            item(title = context.getString(R.string.force_support_smart_case),category = "settings\\feature"),
            item(title = context.getString(R.string.force_dirac_audio),category = "settings\\feature"),
            item(title = context.getString(R.string.force_dolby_audio),category = "settings\\feature"),
            item(title = context.getString(R.string.force_dual_earbuds),category = "settings\\feature"),
            item(title = context.getString(R.string.force_foldable_screen),category = "settings\\feature"),
            item(title = context.getString(R.string.force_fold_or_flip_screen),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_display_remapping),summary = context.getString(R.string.disable_ui_remap_when_unfolded),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_gesture_navigation),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_google_mobile_services),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_storage_info),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_holo_audio),category = "settings\\feature"),
            item(title = context.getString(R.string.force_hd_video),category = "settings\\feature"),
            item(title = context.getString(R.string.auto_grant_install),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_lock_wallpaper),category = "settings\\feature"),
            item(title = context.getString(R.string.light_os),category = "settings\\feature"),
            item(title = context.getString(R.string.force_multi_volume),category = "settings\\feature"),
            item(title = context.getString(R.string.force_app_clone),category = "settings\\feature"),
            item(title = context.getString(R.string.force_adaptive_brightness),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_ota),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_audio_boost),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_ai_image),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_osie_tech),category = "settings\\feature"),
            item(title = context.getString(R.string.force_shutdown_key),category = "settings\\feature"),
            item(title = context.getString(R.string.single_pulse_pwm),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_res_switch),category = "settings\\feature"),
            item(title = context.getString(R.string.manual_refresh_rate),category = "settings\\feature"),
            item(title = context.getString(R.string.default_smart_refresh),category = "settings\\feature"),
            item(title = context.getString(R.string.refresh_rate_notify),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_sell_mode),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_dual_sim),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_single_sim_check),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_anti_voyeur),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_snc_content),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_sound_combo),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_sound_settings),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_audio_input),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_15k_resolution),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_adfr),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_aod),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_aon_face),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_autolayout),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_blade_colormode),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_breeno_suggest),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_brightness_anim),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_cinema_mode),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_oled_colorful),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_custom_color),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_colorful_mode),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_powersaving_color),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_compact_window),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_dc_backlight),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_dynamic_brightness),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_dirac_a2dp),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_dynamic_fps),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_edge_anti_touch),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_5g_support),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_fold_remap),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_gt_mode),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_hdr_alwayson),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_hdr_highlight),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_smart_color_temp2),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_linear_vibration),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_luxun_vibration),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_multi_led_breathing),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_phone_limit),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_pixelworks_x7),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_resolution_switch),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_ringtone_vibration),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_satellite_network),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_spatializer_speaker),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_super_volume2x),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_super_volume3x),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_temp_adjust),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_touchpad_split),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_ultrasonic_fp),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_volume_boost),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_color_ball),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_surround_effect),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_tablet_mode),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_typec_menu),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_ultrasonic_security),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_vibrator_style),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_smart_screenoff),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_richtap_vibrate),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_dirac_v2),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_iris5_display),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_ring_haptic),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_video_osie),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_video_sr),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_deactivate_app),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_haptic_preview),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_modify_devname),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_super_sleep),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_5g_reminder),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_account_dialog),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_app_disable),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_cmiit_auth),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_hyper_vision),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_carrier),category = "settings\\feature"),
            item(title = context.getString(R.string.locale_uk_to_en),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_clear_cache),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_colorful_real),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_confidential),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_cyberpunk),category = "settings\\feature"),
            item(title = context.getString(R.string.auto_resolution),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_oem_unlock),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_auto_rotate),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_app_switch),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_euex),category = "settings\\feature"),
            item(title = context.getString(R.string.force_exp_version),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_film_finger),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_finger_anim),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_fintech_nfc),category = "settings\\feature"),
            item(title = context.getString(R.string.force_flip_device),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_gesture),category = "settings\\feature"),
            item(title = context.getString(R.string.keep_gesture_up),category = "settings\\feature"),
            item(title = context.getString(R.string.more_gesture_up),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_gota_update),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_business_state),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_ultimate_clean),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_hw_version),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_device_id),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_ktv_loopback),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_mms_ringtone),category = "settings\\feature"),
            item(title = context.getString(R.string.move_dc_to_dev),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_network_speed),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_power_wake3),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_sim_signal),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_humming),category = "settings\\feature"),
            item(title = context.getString(R.string.show_kernel_id),category = "settings\\feature"),
            item(title = context.getString(R.string.ignore_repeat_click),category = "settings\\feature"),
            item(title = context.getString(R.string.imei_sv_from_ota),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_light_func),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_marvel),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_portrait_center),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_video_beauty),category = "settings\\feature"),
            item(title = context.getString(R.string.show_2g3g),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_ocloud),category = "settings\\feature"),
            item(title = context.getString(R.string.force_oh_device),category = "settings\\feature"),
            item(title = context.getString(R.string.only_hw_version),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_kddi_au),category = "settings\\feature"),
            item(title = context.getString(R.string.show_operator),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_privacy_email),category = "settings\\feature"),
            item(title = context.getString(R.string.keep_swipe_up),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_ota),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_otg_alarm),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_otg_entry),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_pac_custom),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_privacy),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_fake_base),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_rl_delete),category = "settings\\feature"),
            item(title = context.getString(R.string.force_rlm_device),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_raise_wake),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_recent_task),category = "settings\\feature"),
            item(title = context.getString(R.string.remove_cota_home),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_resize_screen),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_rlm_feedback),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_screen_pin),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_search_index),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_seedling_exp),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_custom_devname),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_cota_devname),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_set_password),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_all_anr),category = "settings\\feature"),
            item(title = context.getString(R.string.show_brand_name),category = "settings\\feature"),
            item(title = context.getString(R.string.show_carrier_config),category = "settings\\feature"),
            item(title = context.getString(R.string.show_carrier_update),category = "settings\\feature"),
            item(title = context.getString(R.string.show_custom_details),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_data_usage),category = "settings\\feature"),
            item(title = context.getString(R.string.show_diagnostic),category = "settings\\feature"),
            item(title = context.getString(R.string.show_os_firstname),category = "settings\\feature"),
            item(title = context.getString(R.string.show_hw_version),category = "settings\\feature"),
            item(title = context.getString(R.string.show_ims_status),category = "settings\\feature"),
            item(title = context.getString(R.string.show_kernel_time),category = "settings\\feature"),
            item(title = context.getString(R.string.show_net_unlock),category = "settings\\feature"),
            item(title = context.getString(R.string.show_never_timeout),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_npu_detail),category = "settings\\feature"),
            item(title = context.getString(R.string.show_processor),category = "settings\\feature"),
            item(title = context.getString(R.string.show_processor_gen2),category = "settings\\feature"),
            item(title = context.getString(R.string.screen_size_cm),category = "settings\\feature"),
            item(title = context.getString(R.string.show_sw_version),category = "settings\\feature"),
            item(title = context.getString(R.string.sw_instead_build),category = "settings\\feature"),
            item(title = context.getString(R.string.show_uicc_unlock),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_sim_lock),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_sim_toolkit),category = "settings\\feature"),
            item(title = context.getString(R.string.force_software_conf),category = "settings\\feature"),
            item(title = context.getString(R.string.special_side_finger),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_circle_search),category = "settings\\feature"),
            item(title = context.getString(R.string.show_custom_ver),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_electronic_label),category = "settings\\feature"),
            item(title = context.getString(R.string.fullscreen_apps),category = "settings\\feature"),
            item(title = context.getString(R.string.smart_gesture),category = "settings\\feature"),
            item(title = context.getString(R.string.show_imsi),category = "settings\\feature"),
            item(title = context.getString(R.string.show_meid),category = "settings\\feature"),
            item(title = context.getString(R.string.member_rcc_show),category = "settings\\feature"),
            item(title = context.getString(R.string.mini_capsule),category = "settings\\feature"),
            item(title = context.getString(R.string.number_recognition),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_oguard),category = "settings\\feature"),
            item(title = context.getString(R.string.oh_india_version),category = "settings\\feature"),
            item(title = context.getString(R.string.usb_tether_boot),category = "settings\\feature"),
            item(title = context.getString(R.string.quick_app_support),category = "settings\\feature"),
            item(title = context.getString(R.string.region_picker),category = "settings\\feature"),
            item(title = context.getString(R.string.enable_roulette),category = "settings\\feature"),
            item(title = context.getString(R.string.show_wfc_dialog),category = "settings\\feature"),
            item(title = context.getString(R.string.smart_touch),category = "settings\\feature"),
            item(title = context.getString(R.string.smart_touch_v2),category = "settings\\feature"),
            item(title = context.getString(R.string.show_sms_number),category = "settings\\feature"),
            item(title = context.getString(R.string.ai_eye_protect),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_edge_panel),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_stable_plan),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_time_change),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_gaze_ringtone),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_user_exp),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_verify_dialog),category = "settings\\feature"),
            item(title = context.getString(R.string.virtual_comm_device),category = "settings\\feature"),
            item(title = context.getString(R.string.virtual_comm_service),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_vowifi_setting),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_volte_setting),category = "settings\\feature"),
            item(title = context.getString(R.string.volte_icon_off),category = "settings\\feature"),
            item(title = context.getString(R.string.disable_wifi_setting),category = "settings\\feature"),
            item(title = context.getString(R.string.hide_install_sources),category = "settings\\feature"),
            item(title = context.getString(R.string.biometric_privacy),category = "settings\\feature"),
            item(title = context.getString(R.string.upload_error_log),category = "settings\\feature"),
            item(title = context.getString(R.string.dirac_sound), category = "settings\\feature"),
            item(title = context.getString(R.string.dolby_support), category = "settings\\feature"),
            item(title = context.getString(R.string.edge_panel), category = "settings\\feature"),
            item(title = context.getString(R.string.resolution_pic), category = "settings\\feature"),
            item(title = context.getString(R.string.sharpness_switch), category = "settings\\feature"),
            item(title = context.getString(R.string.hyper_mode), category = "settings\\feature"),
            item(title = context.getString(R.string.fluid_cloud), category = "settings\\feature"),
            item(title = context.getString(R.string.linear_vibration), category = "settings\\feature"),
            item(title = context.getString(R.string.op7_vibration), category = "settings\\feature"),
            item(title = context.getString(R.string.palm_unlock), category = "settings\\feature"),
            item(title = context.getString(R.string.stealth_security), category = "settings\\feature"),
            item(title = context.getString(R.string.pwm_reboot), category = "settings\\feature"),
            item(title = context.getString(R.string.anti_voyeur), category = "settings\\feature"),
            item(title = context.getString(R.string.enable_redpacket_helper), category = "settings\\feature"),
            item(title = context.getString(R.string.disable_root_dialog),
                category = "health"),
            item(title = context.getString(R.string.remove_recommendations),
                category = "appdetail"),
            item(title = context.getString(R.string.network_speed_indicator),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.network_speed_indicator),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.network_speed_style),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.speed_font_size),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.unit_font_size),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.upload_font_size),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.download_font_size),
                summary = context.getString(R.string.default_value_hint_negative_one),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.slow_speed_threshold),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.hide_on_slow),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.hide_when_both_slow),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.icon_indicator),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.position_speed_indicator_front),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.hide_space),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.hide_bs),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.swap_upload_download),
                category = "systemui\\status_bar_wifi"),
            item(title = context.getString(R.string.disable_72h_verify),
                category = "android"),
            item(title = context.getString(R.string.allow_untrusted_touch),
                category = "android"),
            item(title = context.getString(R.string.remove_app_recommendation_ads),
                category = "quicksearchbox"),
            item(title = context.getString(R.string.accessibility_service_authorize),
                category = "settings"),
            item(title = context.getString(R.string.accessibility_service_direct),
                category = "settings"),
            item(title = context.getString(R.string.smart_accessibility_service),
                summary = context.getString(R.string.whitelist_app_auto_authorization),
                category = "settings"),
            item(title = context.getString(R.string.accessibility_whitelist),
                category = "settings"),
            item(title = context.getString(R.string.remove_installation_frequency_popup),
                category = "appdetail"),
            item(title = context.getString(R.string.remove_attempt_installation_popup),
                category = "appdetail"),
            item(title = context.getString(R.string.remove_version_check),
                category = "appdetail"),
            item(title = context.getString(R.string.remove_security_check),
                category = "appdetail"),
            item(title = context.getString(R.string.enable_alarm_reminder),
                summary = context.getString(R.string.alarm_reminder_description),
                category = "mihealth"),
            item(title = context.getString(R.string.remove_system_update_dialog),
                category = "ota"),
            item(title = context.getString(R.string.remove_system_update_notification),
                category = "ota"),
            item(title = context.getString(R.string.remove_wlan_auto_download_dialog),
                category = "ota"),
            item(title = context.getString(R.string.remove_unlock_and_dmverity_check),
                category = "ota"),
            item(title = context.getString(R.string.enable_mlbb_ai_god_assist),
                category = "games"),
            item(title = context.getString(R.string.remove_oshare_auto_off),
                category = "oshare"),
            item(title = context.getString(R.string.set_anim_level),
                category = "launcher"),
            item(title = context.getString(R.string.hide_call_ringtone),
                category = "incallui")
        )
    }
}
