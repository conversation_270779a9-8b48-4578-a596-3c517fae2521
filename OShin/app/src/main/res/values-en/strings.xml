<resources>
    <string name="app_name">OShin</string>
    <string name="xposed_desc">System support module for ColorOS, RealmeUI, and OxygenOS. Free software – redistribution, sharing, or selling is prohibited! Contact: github@suqi8</string>
    <string name="about_module">This module is made by YukiHookAPI. \nLearn more https://github.com/HighCapable/YuKiHookAPI</string>
    <string name="module_version">Module version: %1$s</string>
    <string name="module_not_activated">Module not activated</string>
    <string name="module_is_activated">Module is activated</string>
    <string name="display_settings">Display settings</string>
    <string name="hide_app_icon_on_launcher">Hide app icons on launcher</string>
    <string name="hide_app_icon_on_launcher_tip">After hiding the app icon, the interface may be closed and will no longer be displayed on the launcher. You can find and open the module settings in EdXposed or LSPosed.</string>
    <string name="hide_app_icon_on_launcher_notice">Note: Be sure to turn off the \"Force apps to show launcher icons\" feature in LSPosed</string>
    <string name="package_manager_services" >Package Management Services</string>
    <string name="Color_Mode">Color Mode</string>
    <string name="Night_Mode">Night Mode</string>
    <string name="Light_Mode">Light Mode</string>
    <string name="Auto_Mode">Auto Mode</string>
    <string name="Device_Name">Device Name</string>
    <string name="ok">OK</string>
    <string name="Device_Memory">Device Memory</string>
    <string name="Search">Search</string>
    <string name="hide_unit">Hide Unit</string>
    <string name="cancel">Cancel</string>
    <string name="ignore">Ignore</string>
    <string name="config_error">Configuration initialization failed</string>
    <string name="not_supported">It seems that you are using an out dated version of LSPosed or LSPosed is not activated, please update LSPosed or try again after activated.</string>
    <string name="usepresig_warn">!! Any apk can override the installed one !!\nBe carefully when installing unknown apk</string>
    <string name="settings">Settings</string>
    <string name="corepatch">This version is for Android 9–14 only.\nPlease use the latest version of the LSPosed.</string>
    <string name="downgr">Allow downgrade</string>
    <string name="downgr_summary">Allow downgrade applications.</string>
    <string name="authcreak">Disable digest verify</string>
    <string name="authcreak_summary">Allows install apps after modify file in apk\n(ignore invalid digest error).</string>
    <string name="digestCreak">Disable compare signatures</string>
    <string name="digestCreak_summary">Allow re-install app with different signatures.</string>
    <string name="UsePreSig">Use installed signatures</string>
    <string name="UsePreSig_summary">Always use signatures from already installed apps when\ninstalling.\n This is extremely <b>dangerous</b>.\n Only enable when really needed!</string>
    <string name="enhancedMode">Enhanced Mode</string>
    <string name="enhancedMode_summary">Pass some validation in the application</string>
    <string name="bypassBlock">Bypass block</string>
    <string name="bypassBlock_summary">Bypass blocklist in some devices like Nothing Phone</string>
    <string name="shared_user_title">Bypass shared user verify</string>
    <string name="shared_user_summary">Allow install app with signature differ from its shared user\n(<b>\'Disable compare signatures\' must be enabled too</b>)</string>
    <string name="disable_verification_agent_title">Disable Package Verification Agent</string>
    <string name="disable_verification_agent_summary">e.g. Google Play Protection</string>
    <string name="func">Function</string>
    <string name="soc_model">SOC Model</string>
    <string name="android_version">Android Version</string>
    <string name="android_api_version">Android API Version</string>
    <string name="system_version">System Version</string>
    <string name="cpu_codename">CPU Codename</string>
    <string name="device_manufacturer">Device Manufacturer</string>
    <string name="supported_abi">Supported ABI</string>
    <string name="android_security_patch">Android Security Patch</string>
    <string name="device_fingerprint">Device Fingerprint</string>
    <string name="yuki_hook_api_version">YukiHookAPI Version</string>
    <string name="compiled_timestamp">Compiled Timestamp</string>
    <string name="compiled_time">Compiled Time</string>
    <string name="official_channel">Official Channel</string>
    <string name="contribute_translation">Contribute to Translation</string>
    <string name="crowdin_contribute_summary">Go to Github to contribute your language translation</string>
    <string name="go_to_his_homepage">Go to His Homepage</string>
    <string name="please_install_cool_apk">Please install the CoolAPK app first</string>
    <string name="home">Home</string>
    <string name="about">About</string>
    <string name="status_bar_clock">Clock Indicator</string>
    <string name="no_start_func">This function is not enabled~</string>
    <string name="clock_style">Clock style</string>
    <string name="preset">Preset</string>
    <string name="geek">Geek</string>
    <string name="clock_size">Clock size</string>
    <string name="font_size">Font size</string>
    <string name="clock_size_summary">Adjust the size of the status bar clock; 0 means no size setting</string>
    <string name="clock_update_time">Clock update time</string>
    <string name="update_time">Update time</string>
    <string name="clock_style_title">Clock Style</string>
    <string name="clock_style_summary">Choose the display style of the clock</string>
    <string name="absolute_value">Absolute value</string>
    <string name="bold_text">Bold</string>
    <string name="alpha_setting">Alpha Value</string>
    <string name="blur_radius_setting">Blur Radius</string>
    <string name="noise_factor_setting">Noise Factor</string>
    <string name="status_bar_time_gravity_center">CENTER Align Center</string>
    <string name="status_bar_time_gravity_top">TOP Align Top</string>
    <string name="status_bar_time_gravity_bottom">BOTTOM Align Bottom</string>
    <string name="status_bar_time_gravity_end">END Align End</string>
    <string name="status_bar_time_gravity_center_horizontal">CENTER_HORIZONTAL Align Horizontally Center</string>
    <string name="status_bar_time_gravity_center_vertical">CENTER_VERTICAL Align Vertically Center</string>
    <string name="status_bar_time_gravity_fill">FILL Fill the Whole Space</string>
    <string name="status_bar_time_gravity_fill_horizontal">FILL_HORIZONTAL Fill Horizontally</string>
    <string name="status_bar_time_gravity_fill_vertical">FILL_VERTICAL Fill Vertically</string>
    <string name="enter_thermal_zone_number">Enter the number after "thermal_zone" in the /sys/devices/virtual/thermal/ folder to specify the source.</string>
    <string name="show_cpu_temp_data">Displaying CPU temperature source data.</string>
    <string name="change_cpu_temp_source">Change CPU temperature source</string>
    <string name="battery_temperature">Battery Temperature</string>
    <string name="cpu_temperature">CPU Temperature</string>
    <string name="module_notice">This module is completely free and open-source. Please comply with the GNU Affero General Public License v3.0. It is prohibited to use this module for traffic diversion, sharing, distributing, or selling. If you see anyone sharing this module elsewhere, please report it to the module author.</string>
    <string name="clock_update_time_title">Clock Update Time</string>
    <string name="clock_update_time_summary">Set the clock update frequency in milliseconds; 0 means no update frequency is set</string>
    <string name="show_years_title">Show Years</string>
    <string name="show_years_summary">e.g., 2023</string>
    <string name="show_month_title">Show Month</string>
    <string name="show_month_summary">e.g., April</string>
    <string name="show_day_title">Show Day</string>
    <string name="show_day_summary">e.g., 5th</string>
    <string name="show_week_title">Show Week</string>
    <string name="show_week_summary">e.g., Monday</string>
    <string name="show_cn_hour_title">Show Chinese Hour</string>
    <string name="show_cn_hour_summary">e.g., Yin Hour</string>
    <string name="showtime_period_title">Show Time Period</string>
    <string name="showtime_period_summary">e.g., Morning</string>
    <string name="show_seconds_title">Show Seconds</string>
    <string name="show_seconds_summary">e.g., xx:xx:41</string>
    <string name="show_millisecond_title">Show Milliseconds</string>
    <string name="show_millisecond_summary">e.g., xx:xx:xx.919</string>
    <string name="hide_space_title">Hide Space</string>
    <string name="hide_space_summary">Hide spaces within the time display</string>
    <string name="dual_row_title">Dual Row Display</string>
    <string name="dual_row_summary">Display time in two rows</string>
    <string name="Researt_app">Researt app</string>
    <string name="confirm_restart_applications">Confirm to restart the following applications?</string>
    <string name="common_settings">Common Settings</string>
    <string name="other_settings">Other Settings</string>
    <string name="module_activated">Module Activated</string>
    <string name="please_activate">Please activate this module in LSPosed</string>
    <string name="app_settings">App settings</string>
    <string name="check_update">Check for Updates</string>
    <string name="by_the_way">By the way</string>
    <string name="alignment">Alignment</string>
    <string name="status_bar_clock_custom_tips">Year, Month, Day:\n y Year\n yy Two-digit year (e.g., 23 for 2023)\n yyyy Four-digit year (e.g., 2023)\n  M Month\n MM Two-digit month (01-12)\n MMM Month abbreviation (e.g., Jan)\n MMMM Full month name (e.g., January)\n  d Day of the month (1-31)\n  D Day of the year (1-366)\n  E Day of the week\n E Abbreviated day of the week (e.g., Tue)\n EEEE Full day of the week (e.g., Tuesday)\n  F Day of the week in the month (1-7)\n  w Week of the year (1-53)\n  W Week of the month (1-5)\n  ---\n  Hour, Minute, Second:\n h Hour in 12-hour format (1-12)\n  H Hour in 24-hour format (0-23)\n  m Minute (0-59)\n  s Second (0-59)\n  S Millisecond (0-999)\n  K Hour in 12-hour format (0-11)\n  k Hour in 24-hour format (1-24)\n  ---\n  AM/PM:\n a AM/PM marker (e.g., AM or PM)\n  ---\n  Time Zone:\n z Time zone\n z Time zone abbreviation (e.g., PST)\n zzzz Full time zone name (e.g., Pacific Standard Time)\n  Z RFC 822 time zone format (e.g., -0800)\n  X ISO 8601 time zone\n X -08 or +08\n XX -0800 or +0800\n XXX -08:00 or +08:00</string>
    <string name="clock_format">Clock format</string>
    <string name="both">Both</string>
    <string name="power">Power</string>
    <string name="current">Current</string>
    <string name="hardware_indicator">Hardware Indicator</string>
    <string name="power_consumption_indicator">Power Consumption Indicator</string>
    <string name="temperature_indicator">Temperature Indicator</string>
    <string name="display_content">Display Content</string>
    <string name="content_not_empty">Content cannot be empty</string>
    <string name="clock_top_margin">Clock Top Margin</string>
    <string name="clock_bottom_margin">Clock Bottom Margin</string>
    <string name="clock_left_margin">Clock Left Margin</string>
    <string name="clock_right_margin">Clock Right Margin</string>
    <string name="addline">Show Divider</string>
    <string name="dual_cell">Dual-cell</string>
    <string name="voltage">Voltage</string>
    <string name="first_line_content">First line content</string>
    <string name="second_line_content">Second line content</string>
    <string name="desktop_icon_and_text_size_multiplier">Desktop Icon and Text Size Multiplier</string>
    <string name="icon_size_limit_note">When the size is greater than 1x, icons will be limited to 1x. This issue will not be resolved for now.</string>
    <string name="nvid_CN">CN China 🇨🇳</string>
    <string name="nvid_TW">TW Taiwan Province of China 🇹🇼</string>
    <string name="nvid_RU">RU Russia 🇷🇺</string>
    <string name="nvid_GDPR_EU">GDPR EU 🇪🇺</string>
    <string name="nvid_GDPR_Europe">GDPR Europe 🇪🇺</string>
    <string name="nvid_IN">IN India 🇮🇳</string>
    <string name="nvid_ID">ID Indonesia 🇮🇩</string>
    <string name="nvid_MY">MY Malaysia 🇲🇾</string>
    <string name="nvid_TH">TH Thailand 🇹🇭</string>
    <string name="nvid_PH">PH Philippines 🇵🇭</string>
    <string name="nvid_SA">SA Saudi Arabia 🇸🇦</string>
    <string name="nvid_LATAM">LATAM Latin America 🇱🇰</string>
    <string name="nvid_BR">BR Brazil 🇧🇷</string>
    <string name="nvid_ME">MEA The Middle East and Africa 🇦🇪</string>
    <string name="nvid_unknown">Device identifier code = %1$s</string>
    <string name="countries_and_regions">Countries and Regions</string>
    <string name="battery_health_good">Good</string>
    <string name="battery_health_overheat">Overheat</string>
    <string name="battery_health_dead">Dead</string>
    <string name="battery_health_over_voltage">Over Voltage</string>
    <string name="battery_health_cold">Cold</string>
    <string name="battery_health_unknown">Unknown</string>
    <string name="battery_health_not_found">Not found</string>
    <string name="battery_status">Battery Status</string>
    <string name="battery_equivalent_capacity">Battery Equivalent Capacity (Typical Value)</string>
    <string name="battery_current_capacity">Current Battery Capacity</string>
    <string name="battery_cycle_count">Battery Cycle Count</string>
    <string name="battery_health">Battery Health</string>
    <string name="battery_full_capacity">Battery Full Capacity</string>
    <string name="magisk_version">Magisk Version</string>
    <string name="ksu_version">KernelSU Version</string>
    <string name="feature_auto_color_picking_enabled">Icon background automatically colored</string>
    <string name="feature_auto_color_picking_warning">When enabled, the background color of the app icon on the feature page will be automatically selected</string>
    <string name="root_permission_error">Please grant permissions</string>
    <string name="retry">Retry</string>
    <string name="retrying">Retrying...</string>
    <string name="exit">Exit</string>
    <string name="root_access_denied">Root access not granted</string>
    <string name="status_bar_icon">Status Bar Icon</string>
    <string name="loading">Loading...</string>
    <string name="default_">Default</string>
    <string name="hide">Hide</string>
    <string name="wifi_icon">Wifi Icon</string>
    <string name="wifi_arrow">Wifi Arrow</string>
    <string name="hide_status_bar">Hide Status Bar</string>
    <string name="enable_all_day_screen_off">Enable All-Day Screen-Off</string>
    <string name="force_trigger_ltpo">Force Trigger LTPO</string>
    <string name="warn">Warn</string>
    <string name="other">Other</string>
    <string name="github_summary">Developers and users are welcome to co-develop with us or make suggestions!</string>
    <string name="thank">Thank</string>
    <string name="contributors">Contributors</string>
    <string name="donors_list">Donors List</string>
    <string name="references">References</string>
    <string name="discussion_group">Discussion Group</string>
    <string name="auto_build_release">Auto Build Release</string>
    <string name="official_website">Official Website</string>
    <string name="open_source_project">Open Source Project</string>
    <string name="closed_source_project">Closed Source Project</string>
    <string name="no_license">No License</string>
    <string name="thanks_open_source_projects">OShin has used part or all of the following open source projects. Thanks to the developers of these projects (in no particular order).</string>
    <string name="coolapk">CoolApk</string>
    <string name="thanks_contributors">Thanks to the following developers or users for their contributions to OShin. OShin would not be where it is today without you! (In no particular order.)</string>
    <string name="oplus_system_services">OnePlus System Services</string>
    <string name="oplus_root_check">Disable Root Check</string>
    <string name="oplus_root_check_summary">Disable root checker</string>
    <string name="clock_margin">Clock Margin</string>
    <string name="clock_format_example">Clock Format Example</string>
    <string name="recent_tasks">Recent Tasks</string>
    <string name="force_display_memory">Force Display Memory</string>
    <string name="status_bar_notification">Status Bar Notification</string>
    <string name="remove_developer_options_notification">Remove Developer Options Notification</string>
    <string name="low_battery_fluid_cloud_off">Disable Fluid Cloud below 20% battery</string>
    <string name="notification_restriction_message">This feature restricts the subsequent pop-up of this notification. Please close any existing notifications manually.</string>
    <string name="remove_and_do_not_disturb_notification">Remove Do Not Disturb Notification</string>
    <string name="hide_launcher_icon">Hide Launcher Icon</string>
    <string name="recommended_features">Recommended Features</string>
    <string name="force_enable_xiaobu_call">Force enable Xiaobu call</string>
    <string name="recent_update">Recent Updates</string>
    <string name="no_introduction">No introduction available yet... 😢</string>
    <string name="remove_full_screen_translation_restriction">Remove full-screen translation restriction</string>
    <string name="enable_ultra_combo">Enable Ultra Combo</string>
    <string name="enable_blur">Enable Blur Effect</string>
    <string name="enable_gradient_blur">Enable Gradient Blur</string>
    <string name="enable_hok_ai_v1">Enable AI Pro Assistant V1</string>
    <string name="enable_hok_ai_v2">Enable AI Pro Assistant V2</string>
    <string name="enable_hok_ai_v3">Enable AI Pro Assistant V3</string>
    <string name="hok_ai_assistant_remove_pkg_restriction">Remove package name restriction for AI God Assistant</string>
    <string name="realme_gt7pro_feature_unlock_device_restriction">Realme GT7 Pro Exclusive: Enabling this feature will remove device model restrictions</string>
    <string name="ai_assistant_global_display">Enabling this feature will display "AI Pro Assistant" in all games</string>
    <string name="hok">Honor of Kings</string>
    <string name="feature_disable_cloud_control">Disable cloud-based feature control</string>
    <string name="remove_package_restriction">Remove package name restrictions</string>
    <string name="enable_all_features">Enable All Features</string>
    <string name="enable_all_features_warning">May cause unexpected issues. Enable with caution.</string>
    <string name="pubg">PUBG Mobile</string>
    <string name="enable_pubg_ai">Enable AI Pro Assistant</string>
    <string name="auto_start_max_limit">Auto-start max limit</string>
    <string name="auto_start_default_hint">Default is 5. No change will keep this value.</string>
    <string name="privacy_title">Privacy Consent Notice</string>
    <string name="privacy_content">
To ensure proper functionality and service quality, we collect specific device information for statistical analysis and optimization purposes:\n
- Device identifier (e.g., Android ID)\n
- App version and runtime status\n
- Device model and OS version\n
- LSPosed version\n
- Magisk/KernelSU version\n
- Network type and carrier information\n\n
All data will be encrypted and processed anonymously, never associated with personal identities. This information is strictly used for product optimization and will not be shared with third parties.
</string>
    <string name="split_screen_multi_window">Split Screen &amp; Multi-Window</string>
    <string name="remove_all_small_window_restrictions">Remove All Small Window Restrictions</string>
    <string name="force_multi_window_mode">Force Enable Multi-Window Mode</string>
    <string name="max_simultaneous_small_windows">Maximum Simultaneous Small Windows</string>
    <string name="small_window_corner_radius">Small Window Corner Radius</string>
    <string name="small_window_focused_shadow">Shadow When Focused</string>
    <string name="small_window_unfocused_shadow">Shadow When Unfocused</string>
    <string name="floating_window">Floating Window</string>
    <string name="default_value_hint_negative_one">-1 for default</string>
    <string name="custom_display_model">Custom Display Model</string>
    <string name="hint_empty_content_default">Use default if empty</string>
    <string name="prompt_search_other_settings">Looking for other settings?</string>
    <string name="remove_swipe_page_ads">Remove ads from swipe page</string>
    <string name="clear_wallet_data_notice">Clear wallet data once after enabling</string>
    <string name="enable_ota_card_bg">Enable OTA Card Background</string>
    <string name="select_background_btn">Select Background Image</string>
    <string name="corner_radius_title">Corner radius</string>
    <string name="force_enable_fold_mode">Force enable Fold mode</string>
    <string name="fold_mode">Fold Mode</string>
    <string name="unfold">Unfold</string>
    <string name="fold">Fold</string>
    <string name="force_enable_fold_dock">Force enable Fold Dock</string>
    <string name="adjust_dock_transparency">Adjust Dock Transparency</string>
    <string name="force_enable_dock_blur">Force enable Dock blur</string>
    <string name="force_enable_dock_blur_undevice">Enabling on unsupported devices will cause the Dock not to be displayed</string>
    <string name="remove_game_filter_root_detection">Remove Root detection for Game Filter</string>
    <string name="remove_all_popup_delays">Remove all popup delays</string>
    <string name="remove_all_popup_delays_eg">Example: Add risky apps to whitelist</string>
    <string name="remove_message_ads">Remove additional message ads</string>
    <string name="force_show_nfc_security_chip">Force show NFC security chip</string>
    <string name="security_payment_remove_risky_fluid_cloud">Remove Fluid Cloud with Payment Environment Having Risks</string>
    <string name="custom_score">Custom Score</string>
    <string name="custom_prompt_content">Custom Prompt Content</string>
    <string name="custom_animation_duration">Custom Animation Duration</string>
    <string name="feature">Feature</string>
    <string name="demo_only_device">Demo-Only Device</string>
    <string name="retail_locked_terminal">Retail-Locked Terminal</string>
    <string name="force_enable_karaoke">Force Enable Karaoke</string>
    <string name="force_enable_all_features">Force Enable All Features</string>
    <string name="force_enable_3d_camera_color">Force Enable 3D Camera Breathing Color</string>
    <string name="force_breathing_light_sync">Force Breathing Light Music Rhythm Sync</string>
    <string name="force_breathing_light_color">Force Breathing Light Color Adjustment</string>
    <string name="emulate_breathing_hardware">Emulate Breathing Light Hardware</string>
    <string name="force_dirac_audio">Force Support Dirac Audio Enhancement</string>
    <string name="force_dolby_audio">Force Support Dolby Audio</string>
    <string name="force_dual_earbuds">Force Support Dual Earbuds Connection</string>
    <string name="force_aon_explorer">Force Support AON Explorer</string>
    <string name="force_enable_app_freeze">Force Enable App Freeze</string>
    <string name="check_ble_audio_whitelist">Check Bluetooth LE Audio Whitelist</string>
    <string name="force_support_wide_gamut">Force Support Wide Color Gamut</string>
    <string name="force_support_color_mode">Force Support Color Mode</string>
    <string name="force_support_hidden_app_feature">Force Support Hidden App Feature</string>
    <string name="force_support_smart_case">Force Support Smart Case</string>
    <string name="force_foldable_screen">Force Foldable Screen</string>
    <string name="force_fold_or_flip_screen">Force Fold or Flip Screen</string>
    <string name="disable_display_remapping">Disable Display Remapping</string>
    <string name="disable_ui_remap_when_unfolded">Disable UI Remapping When Unfolded</string>
    <string name="disable_gesture_navigation">Disable Gesture Navigation</string>
    <string name="disable_google_mobile_services">Disable Google Mobile Services (GMS)</string>
    <string name="hide_storage_info">Hide Storage Information</string>
    <string name="enable_holo_audio">Enable Holo Audio</string>
    <string name="force_hd_video">Force HD Video Support</string>
    <string name="auto_grant_install">Auto Grant Installation Permissions</string>
    <string name="disable_lock_wallpaper">Disable Lockscreen Wallpaper/Poster</string>
    <string name="light_os">Lightweight OS</string>
    <string name="force_multi_volume">Force Per-App Volume Control</string>
    <string name="force_app_clone">Force App Cloning/Dual-Open</string>
    <string name="force_adaptive_brightness">Force Multi-Level Brightness Adjustment</string>
    <string name="disable_ota">Disable OTA Updates</string>
    <string name="enable_audio_boost">Enable Audio Enhancement</string>
    <string name="enable_ai_image">Enable AI Image Enhancement</string>
    <string name="enable_osie_tech">Enable OSIE (Optimal Screen Image Enhancement)</string>
    <string name="force_shutdown_key">Long Press Power Key to Force Shutdown (3s)</string>
    <string name="single_pulse_pwm">Support Single Pulse PWM Dimming</string>
    <string name="disable_res_switch">Disable Resolution Auto-Switching</string>
    <string name="manual_refresh_rate">Manual Refresh Rate Selection</string>
    <string name="default_smart_refresh">Enable Smart Refresh Rate by Default</string>
    <string name="refresh_rate_notify">Show Notification During Refresh Rate Switching</string>
    <string name="enable_sell_mode">Enable Sell Mode</string>
    <string name="enable_dual_sim">Enable Dual SIM</string>
    <string name="disable_single_sim_check">Disable Single SIM Check</string>
    <string name="enable_anti_voyeur">Enable Anti-Voyeur</string>
    <string name="enable_snc_content">Enable SNC Content</string>
    <string name="enable_sound_combo">Enable Sound Combo</string>
    <string name="enable_sound_settings">Enable Sound Settings</string>
    <string name="enable_audio_input">Enable Audio Input</string>
    <string name="enable_15k_resolution">Enable 1.5K Resolution Switch</string>
    <string name="enable_adfr">Enable Adaptive Refresh Rate</string>
    <string name="enable_aod">Enable Always-On Display</string>
    <string name="enable_aon_face">Enable AON Face Detection</string>
    <string name="enable_autolayout">Enable Auto Layout</string>
    <string name="enable_blade_colormode">Enable Blade Color Mode</string>
    <string name="enable_breeno_suggest">Enable Breeno Suggestions</string>
    <string name="enable_brightness_anim">Enable Brightness Animation</string>
    <string name="enable_cinema_mode">Enable Cinema Color Mode</string>
    <string name="enable_oled_colorful">Enable OLED Vivid Mode</string>
    <string name="enable_custom_color">Enable Custom Color</string>
    <string name="enable_colorful_mode">Enable Colorful Mode</string>
    <string name="enable_powersaving_color">Enable Power Saving Color</string>
    <string name="enable_compact_window">Enable Compact Window</string>
    <string name="enable_dc_backlight">Enable DC Dimming</string>
    <string name="enable_dynamic_brightness">Dynamic Brightness Range</string>
    <string name="enable_dirac_a2dp">Enable Dirac Bluetooth Audio</string>
    <string name="enable_dynamic_fps">Dynamic FPS Switch</string>
    <string name="enable_edge_anti_touch">Edge Touch Prevention</string>
    <string name="enable_5g_support">Enable 5G Support</string>
    <string name="disable_fold_remap">Disable Fold Remapping</string>
    <string name="enable_gt_mode">Enable GT Mode</string>
    <string name="enable_hdr_alwayson">HDR Always-On Enhancement</string>
    <string name="enable_hdr_highlight">HDR Video Highlight Mode</string>
    <string name="enable_smart_color_temp2">Smart Color Temp 2.0</string>
    <string name="enable_linear_vibration">Linear Vibration</string>
    <string name="enable_luxun_vibration">LuXun Vibrator</string>
    <string name="enable_multi_led_breathing">Multi-LED Breathing Light</string>
    <string name="enable_phone_limit">Phone Usage Limit</string>
    <string name="enable_pixelworks_x7">Pixelworks X7 Engine</string>
    <string name="enable_resolution_switch">Resolution Switch</string>
    <string name="enable_ringtone_vibration">Ringtone Vibration</string>
    <string name="enable_satellite_network">Satellite Network Support</string>
    <string name="enable_spatializer_speaker">Spatial Audio Speaker</string>
    <string name="enable_super_volume2x">Super Volume 2X</string>
    <string name="enable_super_volume3x">Super Volume 3X</string>
    <string name="enable_temp_adjust">Intelligent Color Temp</string>
    <string name="enable_touchpad_split">Touchpad Split View</string>
    <string name="enable_ultrasonic_fp">Ultrasonic Fingerprint</string>
    <string name="enable_volume_boost">Volume Boost Mode</string>
    <string name="enable_color_ball">Color Adjustment Ball</string>
    <string name="enable_surround_effect">Surround Sound Effect</string>
    <string name="enable_tablet_mode">Tablet Device Mode</string>
    <string name="enable_typec_menu">Type-C Interface Menu</string>
    <string name="enable_ultrasonic_security">Ultrasonic Fingerprint Security</string>
    <string name="enable_vibrator_style">Vibration Style Switch</string>
    <string name="enable_smart_screenoff">Smart Screen Off</string>
    <string name="enable_richtap_vibrate">RichTap Vibration Engine</string>
    <string name="enable_dirac_v2">Dirac Audio V2</string>
    <string name="enable_iris5_display">IRIS5 Display Enhancement</string>
    <string name="enable_ring_haptic">Ringtone Haptic Feedback</string>
    <string name="enable_video_osie">Video OSIE Enhancement</string>
    <string name="enable_video_sr">Video Super Resolution</string>
    <string name="disable_deactivate_app">Disable App Deactivation Restriction</string>
    <string name="disable_haptic_preview">Disable Haptic Preview</string>
    <string name="disable_modify_devname">Disable Device Name Edit</string>
    <string name="enable_super_sleep">Enable Super Sleep</string>
    <string name="disable_5g_reminder">Disable 5G Guidance</string>
    <string name="disable_account_dialog">Disable Account Popup</string>
    <string name="enable_app_disable">Allow Disable System Apps</string>
    <string name="hide_cmiit_auth">Hide CMIIT Auth</string>
    <string name="enable_hyper_vision">Hyper Vision Mode</string>
    <string name="disable_carrier">Hide Carrier Settings</string>
    <string name="locale_uk_to_en">UK to US English</string>
    <string name="disable_clear_cache">Disable Cache Clear</string>
    <string name="enable_colorful_real">True Color Display</string>
    <string name="disable_confidential">Bypass Confidential</string>
    <string name="enable_cyberpunk">Cyberpunk Theme</string>
    <string name="auto_resolution">Auto Resolution Default</string>
    <string name="enable_oem_unlock">Enable OEM Unlock</string>
    <string name="disable_auto_rotate">Hide Auto Rotate</string>
    <string name="disable_app_switch">Block App Switching</string>
    <string name="enable_euex">EU EX Version</string>
    <string name="force_exp_version">Force Global Version</string>
    <string name="enable_film_finger">Cinematic Fingerprint</string>
    <string name="enable_finger_anim">Enable Fingerprint Animation</string>
    <string name="enable_fintech_nfc">Fintech NFC Support</string>
    <string name="force_flip_device">Force Flip Device</string>
    <string name="disable_gesture">Disable Fullscreen Gesture</string>
    <string name="keep_gesture_up">Force Keep Swipe Up</string>
    <string name="more_gesture_up">Enhanced Gestures</string>
    <string name="enable_gota_update">GOTA System Update</string>
    <string name="enable_business_state">Business Statement</string>
    <string name="enable_ultimate_clean">Ultimate Cleanup</string>
    <string name="hide_hw_version">Hide Hardware Version</string>
    <string name="hide_device_id">Hide Device ID</string>
    <string name="hide_ktv_loopback">Hide KTV Loopback</string>
    <string name="hide_mms_ringtone">Hide MMS Ringtone</string>
    <string name="move_dc_to_dev">Move DC Dimming to Dev</string>
    <string name="hide_network_speed">Hide Network Speed</string>
    <string name="hide_power_wake3">Hide Power Wake Item3</string>
    <string name="hide_sim_signal">Hide SIM Signal</string>
    <string name="enable_humming">Humming Recognition</string>
    <string name="show_kernel_id">Show Kernel ID</string>
    <string name="ignore_repeat_click">Anti Repeat Click</string>
    <string name="imei_sv_from_ota">IMEI-SV from OTA</string>
    <string name="enable_light_func">Light Sensor Feature</string>
    <string name="enable_marvel">Marvel Edition</string>
    <string name="hide_portrait_center">Hide Portrait Center</string>
    <string name="hide_video_beauty">Hide Video Beauty</string>
    <string name="show_2g3g">Show 2G/3G Options</string>
    <string name="disable_ocloud">Disable OCloud</string>
    <string name="force_oh_device">Force OH Device Exp</string>
    <string name="only_hw_version">Only Hardware Version</string>
    <string name="enable_kddi_au">KDDI Show AU</string>
    <string name="show_operator">Show Operator Name</string>
    <string name="hide_privacy_email">Hide Privacy Email</string>
    <string name="keep_swipe_up">Keep Swipe Up</string>
    <string name="disable_otg_alarm">Disable OTG Alarm</string>
    <string name="disable_otg_entry">Disable OTG Entry</string>
    <string name="enable_pac_custom">PAC Custom Version</string>
    <string name="disable_privacy">Disable Privacy Mode</string>
    <string name="hide_fake_base">Hide Fake Base Station</string>
    <string name="enable_rl_delete">RL Language Delete</string>
    <string name="force_rlm_device">Force RLM Device</string>
    <string name="enable_raise_wake">Raise to Wake</string>
    <string name="disable_recent_task">Disable Recent Tasks</string>
    <string name="remove_cota_home">Remove COTA Home Tag</string>
    <string name="disable_resize_screen">Disable Resize Screen</string>
    <string name="enable_rlm_feedback">RLM Feedback V2</string>
    <string name="disable_screen_pin">Disable Screen Pinning</string>
    <string name="disable_search_index">Disable Search Index</string>
    <string name="enable_seedling_exp">Smart Suggestion Labs</string>
    <string name="enable_custom_devname">Custom Device Name</string>
    <string name="enable_cota_devname">COTA Device Name</string>
    <string name="disable_set_password">Disable Password Set</string>
    <string name="hide_all_anr">Hide All ANR</string>
    <string name="show_brand_name">Show Brand Name</string>
    <string name="show_carrier_config">Show Carrier Config Ver</string>
    <string name="show_carrier_update">Show Carrier Update</string>
    <string name="show_custom_details">Show Custom Details</string>
    <string name="hide_data_usage">Hide Data Usage</string>
    <string name="show_diagnostic">Show Diagnostic Info</string>
    <string name="show_os_firstname">Show OS First Name</string>
    <string name="show_hw_version">About Phone HW Version</string>
    <string name="show_ims_status">Show IMS Status</string>
    <string name="show_kernel_time">Kernel Build Time</string>
    <string name="show_net_unlock">Network Unlock</string>
    <string name="show_never_timeout">Never Sleep</string>
    <string name="hide_npu_detail">Hide NPU Details</string>
    <string name="show_processor">Processor Details</string>
    <string name="show_processor_gen2">Gen2 Processor</string>
    <string name="screen_size_cm">Screen Size(cm)</string>
    <string name="show_sw_version">Software Version</string>
    <string name="sw_instead_build">SW Version Instead</string>
    <string name="show_uicc_unlock">UICC Unlock</string>
    <string name="enable_sim_lock">SIM Lock State</string>
    <string name="hide_sim_toolkit">Hide SIM Toolkit</string>
    <string name="force_software_conf">Force SW Confidential</string>
    <string name="special_side_finger">Folded Side Finger</string>
    <string name="enable_circle_search">Circle to Search</string>
    <string name="show_custom_ver">Show Custom Version</string>
    <string name="enable_electronic_label">Electronic Label</string>
    <string name="fullscreen_apps">Fullscreen Apps</string>
    <string name="smart_gesture">Smart Gesture</string>
    <string name="show_imsi">Show IMSI</string>
    <string name="show_meid">Show MEID</string>
    <string name="member_rcc_show">Member RCC</string>
    <string name="mini_capsule">Mini Capsule</string>
    <string name="number_recognition">Number Recognition</string>
    <string name="enable_oguard">OGuard Info</string>
    <string name="oh_india_version">OH India</string>
    <string name="usb_tether_boot">USB Tether Boot</string>
    <string name="quick_app_support">Quick App</string>
    <string name="region_picker">Region Picker</string>
    <string name="enable_roulette">Roulette Support</string>
    <string name="show_wfc_dialog">Show WFC Dialog</string>
    <string name="smart_touch">Smart Touch</string>
    <string name="smart_touch_v2">Smart Touch V2</string>
    <string name="show_sms_number">Show SMS Number</string>
    <string name="ai_eye_protect">AI Eye Protect</string>
    <string name="disable_edge_panel">Disable Edge Panel</string>
    <string name="disable_stable_plan">Disable Stable Plan</string>
    <string name="disable_time_change">Disable Time Change</string>
    <string name="disable_gaze_ringtone">Disable Gaze Ringtone</string>
    <string name="disable_user_exp">Disable User Exp</string>
    <string name="disable_verify_dialog">Disable Verify Dialog</string>
    <string name="virtual_comm_device">Virtual Comm Device</string>
    <string name="virtual_comm_service">Virtual Comm Service</string>
    <string name="disable_vowifi_setting">Disable VoWIFI</string>
    <string name="disable_volte_setting">Disable VoLTE Settings</string>
    <string name="volte_icon_off">VoLTE Icon Off</string>
    <string name="disable_wifi_setting">Hide WiFi Settings</string>
    <string name="hide_install_sources">Hide Install Sources</string>
    <string name="biometric_privacy">Biometric Privacy</string>
    <string name="upload_error_log">Upload Error Logs</string>
    <string name="dirac_sound">Dirac Sound</string>
    <string name="fluid_cloud">Fluid Cloud</string>
    <string name="hyper_mode">Hyper Mode</string>
    <string name="edge_panel">Edge Panel</string>
    <string name="linear_vibration">Linear Vibration</string>
    <string name="op7_vibration">OP7 Vibration</string>
    <string name="dolby_support">Dolby Atmos</string>
    <string name="stealth_security">Stealth Security</string>
    <string name="pwm_reboot">PWM Reboot</string>
    <string name="anti_voyeur">Anti-Voyeur</string>
    <string name="taskbar_support">Taskbar</string>
    <string name="resolution_pic">Resolution Picture</string>
    <string name="brightness_rm">Brightness RM</string>
    <string name="sharpness_switch">Sharpness Switch</string>
    <string name="palm_unlock">Palm Unlock</string>
    <string name="enable_redpacket_helper">Enable Red Packet Assistant</string>
    <string name="disable_root_dialog">Remove Root Detection Popup</string>
    <string name="remove_recommendations">Remove \"You May Also Like\"</string>
    <string name="help">Help</string>
</resources>
