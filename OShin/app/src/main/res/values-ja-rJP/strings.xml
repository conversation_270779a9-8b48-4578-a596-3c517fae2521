<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">OShin</string>
    <string name="xposed_desc">System support module for ColorOS, RealmeUI, and OxygenOS. Free software – redistribution, sharing, or selling is prohibited! Contact: github@suqi8</string>
    <string name="about_module">This module is made by YukiHookAPI. \nLearn more https://github.com/HighCapable/YuKiHookAPI</string>
    <string name="module_version">モジュールのバージョン: %1$s</string>
    <string name="module_not_activated">モジュールが非アクティブです</string>
    <string name="module_is_activated">モジュールはアクティブです</string>
    <string name="display_settings">画面の設定</string>
    <string name="hide_app_icon_on_launcher">ランチャーからアプリアイコンを隠す</string>
    <string name="hide_app_icon_on_launcher_tip">After hiding the app icon, the interface may be closed and will no longer be displayed on the launcher. You can find and open the module settings in EdXposed or LSPosed.</string>
    <string name="hide_app_icon_on_launcher_notice">Note: Be sure to turn off the \"Force apps to show launcher icons\" feature in LSPosed</string>
    <string name="package_manager_services">パッケージ管理サービス</string>
    <string name="Color_Mode">カラーモード</string>
    <string name="Night_Mode">ナイトモード</string>
    <string name="Light_Mode">ライトモード</string>
    <string name="Auto_Mode">自動モード</string>
    <string name="Device_Name">デバイス名</string>
    <string name="ok">OK</string>
    <string name="Device_Memory">デバイスのメモリ</string>
    <string name="Search">検索</string>
    <string name="hide_unit">単位を隠す</string>
    <string name="cancel">キャンセル</string>
    <string name="ignore">無視</string>
    <string name="config_error">設定の初期化に失敗しました</string>
    <string name="not_supported">It seems that you are using an out dated version of LSPosed or LSPosed is not activated, please update LSPosed or try again after activated.</string>
    <string name="usepresig_warn">!! Any apk can override the installed one !!\nBe carefully when installing unknown apk</string>
    <string name="settings">設定</string>
    <string name="corepatch">このバージョンは Android 9–14 のみです。\n最新の LSPosed を使用してください。</string>
    <string name="downgr">ダウングレードを許可</string>
    <string name="downgr_summary">アプリのダウングレードを許可します。</string>
    <string name="authcreak">Digest 認証を無効化</string>
    <string name="authcreak_summary">Allows install apps after modify file in apk\n(ignore invalid digest error).</string>
    <string name="digestCreak">Disable compare signatures</string>
    <string name="digestCreak_summary">Allow re-install app with different signatures.</string>
    <string name="UsePreSig">インストール済みの署名を使用する</string>
    <string name="UsePreSig_summary">Always use signatures from already installed apps when\ninstalling.\n This is extremely <b>dangerous</b>.\n Only enable when really needed!</string>
    <string name="enhancedMode">エンハンスドモード</string>
    <string name="enhancedMode_summary">Pass some validation in the application</string>
    <string name="bypassBlock">ブロックをバイパス</string>
    <string name="bypassBlock_summary">Bypass blocklist in some devices like Nothing Phone</string>
    <string name="shared_user_title">Bypass shared user verify</string>
    <string name="shared_user_summary">Allow install app with signature differ from its shared user\n(<b>\'Disable compare signatures\' must be enabled too</b>)</string>
    <string name="disable_verification_agent_title">Disable Package Verification Agent</string>
    <string name="disable_verification_agent_summary">例: Google Play プロテクト</string>
    <string name="func">機能</string>
    <string name="soc_model">SOC モデル</string>
    <string name="android_version">Android バージョン</string>
    <string name="android_api_version">Android API バージョン</string>
    <string name="system_version">システムバージョン</string>
    <string name="cpu_codename">CPU コードネーム</string>
    <string name="device_manufacturer">デバイスのメーカー</string>
    <string name="supported_abi">サポートしている ABI</string>
    <string name="android_security_patch">Android セキュリティパッチ</string>
    <string name="device_fingerprint">デバイスの Fingerprint</string>
    <string name="yuki_hook_api_version">YukiHookAPI バージョン</string>
    <string name="compiled_timestamp">コンパイルされたタイムスタンプ</string>
    <string name="compiled_time">コンパイルされた日時</string>
    <string name="official_channel">公式チャンネル</string>
    <string name="contribute_translation">翻訳に貢献する</string>
    <string name="crowdin_contribute_summary">Github で言語の翻訳に貢献しましょう</string>
    <string name="go_to_his_homepage">Go to His Homepage</string>
    <string name="please_install_cool_apk">Please install the CoolAPK app first</string>
    <string name="home">ホーム</string>
    <string name="about">アプリについて</string>
    <string name="status_bar_clock">ステータスバーの時計</string>
    <string name="no_start_func">This function is not enabled~</string>
    <string name="clock_style">時計のスタイル</string>
    <string name="preset">プリセット</string>
    <string name="geek">ギーク</string>
    <string name="clock_size">時計のサイズ</string>
    <string name="font_size">フォントのサイズ</string>
    <string name="clock_size_summary">Adjust the size of the status bar clock; 0 means no size setting</string>
    <string name="clock_update_time">Clock update time</string>
    <string name="update_time">Update time</string>
    <!-- Clock Style -->
    <string name="clock_style_title">時計のスタイル</string>
    <string name="clock_style_summary">Choose the display style of the clock</string>
    <string name="absolute_value">Absolute value</string>
    <string name="bold_text">太字</string>
    <string name="alpha_setting">アルファ値</string>
    <string name="blur_radius_setting">Blur Radius</string>
    <string name="noise_factor_setting">Noise Factor</string>
    <string name="status_bar_time_gravity_center">CENTER Align Center</string>
    <string name="status_bar_time_gravity_top">TOP Align Top</string>
    <string name="status_bar_time_gravity_bottom">BOTTOM Align Bottom</string>
    <string name="status_bar_time_gravity_end">END Align End</string>
    <string name="status_bar_time_gravity_center_horizontal">CENTER_HORIZONTAL Align Horizontally Center</string>
    <string name="status_bar_time_gravity_center_vertical">CENTER_VERTICAL Align Vertically Center</string>
    <string name="status_bar_time_gravity_fill">FILL Fill the Whole Space</string>
    <string name="status_bar_time_gravity_fill_horizontal">FILL_HORIZONTAL Fill Horizontally</string>
    <string name="status_bar_time_gravity_fill_vertical">FILL_VERTICAL Fill Vertically</string>
    <string name="enter_thermal_zone_number">Enter the number after \"thermal_zone\" in the /sys/devices/virtual/thermal/ folder to specify the source.</string>
    <string name="show_cpu_temp_data">Displaying CPU temperature source data.</string>
    <string name="change_cpu_temp_source">CPU 温度のソースを変更</string>
    <string name="battery_temperature">バッテリーの温度</string>
    <string name="cpu_temperature">CPU の温度</string>
    <string name="module_notice">This module is completely free and open-source. Please comply with the GNU Affero General Public License v3.0. It is prohibited to use this module for traffic diversion, sharing, distributing, or selling. If you see anyone sharing this module elsewhere, please report it to the module author.</string>
    <string name="clock_update_time_title">Clock Update Time</string>
    <string name="clock_update_time_summary">Set the clock update frequency in milliseconds; 0 means no update frequency is set</string>
    <string name="show_years_title">年を表示</string>
    <string name="show_years_summary">例: 2023</string>
    <string name="show_month_title">月を表示</string>
    <string name="show_month_summary">例: 4 月</string>
    <string name="show_day_title">日を表示</string>
    <string name="show_day_summary">例: 5 日</string>
    <string name="show_week_title">週を表示</string>
    <string name="show_week_summary">例: 月曜日</string>
    <string name="show_cn_hour_title">Show Chinese Hour</string>
    <string name="show_cn_hour_summary">e.g., Yin Hour</string>
    <string name="showtime_period_title">Show Time Period</string>
    <string name="showtime_period_summary">e.g., Morning</string>
    <string name="show_seconds_title">秒を表示</string>
    <string name="show_seconds_summary">例: xx:xx:41</string>
    <string name="show_millisecond_title">ミリ秒を表示</string>
    <string name="show_millisecond_summary">例: xx:xx:xx.919</string>
    <string name="hide_space_title">空白を隠す</string>
    <string name="hide_space_summary">時間表示内の空白を隠します</string>
    <string name="dual_row_title">多段表示</string>
    <string name="dual_row_summary">時計を二行で表示します</string>
    <string name="Researt_app">アプリを再起動</string>
    <string name="confirm_restart_applications">Confirm to restart the following applications?</string>
    <string name="common_settings">共通の設定</string>
    <string name="other_settings">その他の設定</string>
    <string name="module_activated">モジュールはアクティブです</string>
    <string name="please_activate">Please activate this module in LSPosed</string>
    <string name="app_settings">アプリの設定</string>
    <string name="check_update">更新を確認</string>
    <string name="by_the_way">By the way</string>
    <string name="alignment">Alignment</string>
    <string name="status_bar_clock_custom_tips">Year, Month, Day:\n y Year\n yy Two-digit year (e.g., 23 for 2023)\n yyyy Four-digit year (e.g., 2023)\n  M Month\n MM Two-digit month (01-12)\n MMM Month abbreviation (e.g., Jan)\n MMMM Full month name (e.g., January)\n  d Day of the month (1-31)\n  D Day of the year (1-366)\n  E Day of the week\n E Abbreviated day of the week (e.g., Tue)\n EEEE Full day of the week (e.g., Tuesday)\n  F Day of the week in the month (1-7)\n  w Week of the year (1-53)\n  W Week of the month (1-5)\n  ---\n  Hour, Minute, Second:\n h Hour in 12-hour format (1-12)\n  H Hour in 24-hour format (0-23)\n  m Minute (0-59)\n  s Second (0-59)\n  S Millisecond (0-999)\n  K Hour in 12-hour format (0-11)\n  k Hour in 24-hour format (1-24)\n  ---\n  AM/PM:\n a AM/PM marker (e.g., AM or PM)\n  ---\n  Time Zone:\n z Time zone\n z Time zone abbreviation (e.g., PST)\n zzzz Full time zone name (e.g., Pacific Standard Time)\n  Z RFC 822 time zone format (e.g., -0800)\n  X ISO 8601 time zone\n X -08 or +08\n XX -0800 or +0800\n XXX -08:00 or +08:00</string>
    <string name="clock_format">時計のフォーマット</string>
    <string name="both">両方</string>
    <string name="power">電源</string>
    <string name="current">現在</string>
    <string name="hardware_indicator">ハードウェアインジケーター</string>
    <string name="power_consumption_indicator">Power Consumption Indicator</string>
    <string name="temperature_indicator">温度インジケーター</string>
    <string name="display_content">画面のコンテンツ</string>
    <string name="content_not_empty">コンテンツは空にできません</string>
    <string name="clock_top_margin">Clock Top Margin</string>
    <string name="clock_bottom_margin">Clock Bottom Margin</string>
    <string name="clock_left_margin">Clock Left Margin</string>
    <string name="clock_right_margin">Clock Right Margin</string>
    <string name="addline">Show Divider</string>
    <string name="dual_cell">デュアルセル</string>
    <string name="voltage">電圧</string>
    <string name="first_line_content">最初のラインのコンテンツ</string>
    <string name="second_line_content">二番目のラインのコンテンツ</string>
    <string name="desktop_icon_and_text_size_multiplier">Desktop Icon and Text Size Multiplier</string>
    <string name="icon_size_limit_note">When the size is greater than 1x, icons will be limited to 1x. This issue will not be resolved for now.</string>
    <string name="nvid_CN">CN China 🇨🇳</string>
    <string name="nvid_TW">TW Taiwan Province of China 🇹🇼</string>
    <string name="nvid_RU">RU Russia 🇷🇺</string>
    <string name="nvid_GDPR_EU">GDPR EU 🇪🇺</string>
    <string name="nvid_GDPR_Europe">GDPR Europe 🇪🇺</string>
    <string name="nvid_IN">IN India 🇮🇳</string>
    <string name="nvid_ID">ID Indonesia 🇮🇩</string>
    <string name="nvid_MY">MY Malaysia 🇲🇾</string>
    <string name="nvid_TH">TH Thailand 🇹🇭</string>
    <string name="nvid_PH">PH Philippines 🇵🇭</string>
    <string name="nvid_SA">SA Saudi Arabia 🇸🇦</string>
    <string name="nvid_LATAM">LATAM Latin America 🇱🇰</string>
    <string name="nvid_BR">BR Brazil 🇧🇷</string>
    <string name="nvid_ME">MEA The Middle East and Africa 🇦🇪</string>
    <string name="nvid_unknown">Device identifier code = %1$s</string>
    <string name="countries_and_regions">国と地域</string>
    <string name="battery_health_good">良好</string>
    <string name="battery_health_overheat">オーバーヒート</string>
    <string name="battery_health_dead">死亡</string>
    <string name="battery_health_over_voltage">過電圧</string>
    <string name="battery_health_cold">冷却</string>
    <string name="battery_health_unknown">不明</string>
    <string name="battery_health_not_found">ありません</string>
    <string name="battery_status">バッテリーステータス</string>
    <string name="battery_equivalent_capacity">Battery Equivalent Capacity (Typical Value)</string>
    <string name="battery_current_capacity">現在のバッテリー容量</string>
    <string name="battery_cycle_count">バッテリーサイクル数</string>
    <string name="battery_health">バッテリーの健康度</string>
    <string name="battery_full_capacity">完全なバッテリー容量</string>
    <string name="magisk_version">Magisk バージョン</string>
    <string name="ksu_version">KernelSU バージョン</string>
    <string name="feature_auto_color_picking_enabled">Icon background automatically colored</string>
    <string name="feature_auto_color_picking_warning">When enabled, the background color of the app icon on the feature page will be automatically selected</string>
    <string name="root_permission_error">Please grant root permissions</string>
    <string name="retry">再試行</string>
    <string name="retrying">再試行中...</string>
    <string name="exit">終了</string>
    <string name="root_access_denied">Root アクセスが許可されていません</string>
    <string name="status_bar_icon">ステータスバーアイコン</string>
    <string name="loading">読み込み中...</string>
    <string name="default_">デフォルト</string>
    <string name="hide">隠す</string>
    <string name="wifi_icon">Wi-Fi アイコン</string>
    <string name="wifi_arrow">Wi-Fi の矢印</string>
    <string name="hide_status_bar">ステータスバーを隠す</string>
    <string name="enable_all_day_screen_off">Enable All-Day Screen-Off</string>
    <string name="force_trigger_ltpo">LTPO のトリガーを強制する</string>
    <string name="warn">警告</string>
    <string name="other">その他</string>
    <string name="github_summary">Developers and users are welcome to co-develop with us or make suggestions!</string>
    <string name="thank">Thank</string>
    <string name="contributors">貢献者</string>
    <string name="donors_list">寄付者の一覧</string>
    <string name="references">リファレンス</string>
    <string name="discussion_group">ディスカッショングループ</string>
    <string name="auto_build_release">自動ビルドリリース</string>
    <string name="official_website">公式 Web サイト</string>
    <string name="open_source_project">オープンソースプロジェクト</string>
    <string name="closed_source_project">クローズドソースプロジェクト</string>
    <string name="no_license">No License</string>
    <string name="thanks_open_source_projects">OShin has used part or all of the following open source projects. Thanks to the developers of these projects (in no particular order).</string>
    <string name="coolapk">CoolApk</string>
    <string name="thanks_contributors">Thanks to the following developers or users for their contributions to OShin. OShin would not be where it is today without you! (In no particular order.)</string>
    <string name="oplus_system_services">OnePlus システムサービス</string>
    <string name="oplus_root_check">Root チェックを無効化</string>
    <string name="oplus_root_check_summary">Root チェッカーを無効化します</string>
    <string name="clock_margin">Clock Margin</string>
    <string name="clock_format_example">Clock Format Example</string>
    <string name="recent_tasks">Recent Tasks</string>
    <string name="force_display_memory">Force Display Memory</string>
    <string name="status_bar_notification">Status Bar Notification</string>
    <string name="remove_developer_options_notification">Remove Developer Options Notification</string>
    <string name="low_battery_fluid_cloud_off">Disable Fluid Cloud below 20% battery</string>
    <string name="notification_restriction_message">This feature restricts the subsequent pop-up of this notification. Please close any existing notifications manually.</string>
    <string name="remove_and_do_not_disturb_notification">Remove Do Not Disturb Notification</string>
</resources>
