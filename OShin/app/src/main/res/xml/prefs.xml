<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <Preference android:summary="@string/corepatch" />
    <PreferenceCategory android:title="@string/settings">
        <SwitchPreference
            android:key="downgrade"
            android:title="@string/downgr"
            android:summary="@string/downgr_summary"
            android:defaultValue="true" />
        <SwitchPreference
            android:key="authcreak"
            android:title="@string/authcreak"
            android:summary="@string/authcreak_summary"
            android:defaultValue="false" />
        <SwitchPreference
            android:key="digestCreak"
            android:title="@string/digestCreak"
            android:summary="@string/digestCreak_summary"
            android:defaultValue="true" />

        <SwitchPreference
            android:key="UsePreSig"
            android:title="@string/UsePreSig"
            android:summary="@string/UsePreSig_summary"
            android:defaultValue="false" />

        <SwitchPreference
            android:key="enhancedMode"
            android:title="@string/enhancedMode"
            android:summary="@string/enhancedMode_summary"
            android:defaultValue="false" />
        <SwitchPreference
            android:key="bypassBlock"
            android:title="@string/bypassBlock"
            android:summary="@string/bypassBlock_summary"
            android:defaultValue="true" />

        <SwitchPreference
            android:key="sharedUser"
            android:title="@string/shared_user_title"
            android:summary="@string/shared_user_summary"
            android:defaultValue="false" />

        <SwitchPreference
            android:key="disableVerificationAgent"
            android:title="@string/disable_verification_agent_title"
            android:summary="@string/disable_verification_agent_summary"
            android:defaultValue="true" />
    </PreferenceCategory>

</PreferenceScreen>
