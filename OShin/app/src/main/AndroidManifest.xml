<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:networkSecurityConfig="@xml/network_security_config">
    <!-- 引用网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 访问网络状态权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 访问 Wi-Fi 状态权限 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 杀后台进程权限 -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <!-- 读取电话状态权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE"
            tools:ignore="SelectedPhotoAccess" />
    <!-- 读取媒体图片权限 -->
    <uses-permission
            android:name="android.permission.READ_MEDIA_IMAGES"
            tools:ignore="SelectedPhotoAccess" />
    <!-- 读取媒体视频权限 -->
    <uses-permission
            android:name="android.permission.READ_MEDIA_VIDEO"
            tools:ignore="SelectedPhotoAccess" />
    <!-- 读取媒体音频权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- 请求安装包权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- 检测截屏权限（部分系统支持） -->
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />
    <!-- 修改系统设置权限 -->
    <uses-permission
            android:name="android.permission.WRITE_SETTINGS"
            tools:ignore="ProtectedPermissions" />
    <!-- 修改系统安全设置权限 -->
    <uses-permission
            android:name="android.permission.WRITE_SECURE_SETTINGS"
            tools:ignore="ProtectedPermissions" />
    <!-- 管理外部存储权限（需声明分区存储豁免） -->
    <uses-permission
            android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
            tools:ignore="ScopedStorage" />
    <!-- 查询所有已安装包名权限 -->
    <uses-permission
            android:name="android.permission.QUERY_ALL_PACKAGES"
            tools:ignore="QueryAllPackagesPermission" />
    <!-- 获取已安装应用权限（自定义权限） -->
    <uses-permission android:name="com.android.permission.GET_INSTALLED_APPS" />

    <application
            android:usesCleartextTraffic="true"
            android:name=".application.DefaultApplication"
            android:enableOnBackInvokedCallback="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:supportsRtl="true"
            android:theme="@style/Theme.AppDefault"
            android:requestLegacyExternalStorage="true">
        <meta-data
                android:name="xposedmodule"
                android:value="true" />
        <meta-data
                android:name="xposedscope"
                android:resource="@array/module_scope" />
        <meta-data
                android:name="xposeddescription"
                android:value="@string/xposed_desc" />
        <meta-data
                android:name="xposedminversion"
                android:value="93" />

        <activity
                android:name=".MainActivity"
                android:exported="true"
                android:screenOrientation="behind"
                android:windowSoftInputMode="adjustResize"
                tools:ignore="DiscouragedApi,DuplicateActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="de.robv.android.xposed.category.MODULE_SETTINGS" />
            </intent-filter>
        </activity>

        <activity-alias
                android:name=".Home"
                android:exported="true"
                android:label="@string/app_name"
                android:windowSoftInputMode="adjustResize"
                android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="um.67c7dea68f232a05f127781e" />
            </intent-filter>
        </activity-alias>
        <activity-alias
                android:name=".XposedHome"
                android:exported="true"
                android:label="@string/app_name"
                android:windowSoftInputMode="adjustResize"
                android:targetActivity=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="de.robv.android.xposed.category.MODULE_SETTINGS" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="um.67c7dea68f232a05f127781e" />
            </intent-filter>
        </activity-alias>
    </application>

</manifest>
