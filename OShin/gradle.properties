# Compiler Configuration
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
android.useAndroidX=true
android.nonTransitiveRClass=true
kotlin.code.style=official
kotlin.incremental.useClasspathSnapshot=true
org.gradle.unsafe.configuration-cache=true
org.gradle.unsafe.configuration-cache-problems=warn
android.enableCoreLibraryDesugaringCache=true
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
android.buildTypes.release.minifyEnabled=true
android.buildTypes.release.proguardFiles='proguard-rules.pro'
org.gradle.warning.mode=none
