[versions]
activityComposeVersion = "1.10.1"
api = "1.2.1"
apiVersion = "82"
appcompatVersion = "1.7.0"
asms = "1.8.7.2"
coilNetworkOkhttpVersion = "3.1.0"
commonVersion = "9.4.7"
composeBomVersion = "2025.05.01"
composeneumorphism = "1.0.0-alpha02"
composeShimmer = "1.3.1"
constraintlayoutVersion = "2.2.1"
coreKtxVersion = "1.16.0"
dexkitVersion = "2.0.3"
drawabletoolboxVersion = "1.0.7"
espressoCoreVersion = "3.6.1"
expandablebottombar = "1.5.4"
ezxhelper = "2.2.1"
gson = "2.13.1"
haze = "1.5.0"
composeBom = "2025.05.01"
junit = "4.13.2"
junitVersion = "1.2.1"
kspXposedVersion = "1.2.1"
lifecycleRuntimeKtxVersion = "2.9.0"
lottieComposeVersion = "6.6.2"
materialVersion = "1.13.0-alpha13"
miuix = "0.4.5"
navigationCompose = "2.9.0"
okhttpVersion = "5.0.0-alpha.14"
paletteKtx = "1.0.0"
roomRuntime = "2.7.1"
toolbarComposeVersion = "2.3.5"
uyumao = "1.1.4"
xxpermissionsVersion = "20.0"
[libraries]
androidx-activity-compose-v190 = { module = "androidx.activity:activity-compose", version.ref = "activityComposeVersion" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompatVersion" }
androidx-compose-bom-v20240600 = { module = "androidx.compose:compose-bom", version.ref = "composeBomVersion" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayoutVersion" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtxVersion" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCoreVersion" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "junitVersion" }
androidx-lifecycle-runtime-ktx-v282 = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtxVersion" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
androidx-navigation-runtime-ktx = { module = "androidx.navigation:navigation-runtime-ktx", version.ref = "navigationCompose" }
androidx-palette-ktx = { module = "androidx.palette:palette-ktx", version.ref = "paletteKtx" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomRuntime" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomRuntime" }
api = { module = "de.robv.android.xposed:api", version.ref = "apiVersion" }
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coilNetworkOkhttpVersion" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coilNetworkOkhttpVersion" }
common = { module = "com.umeng.umsdk:common", version.ref = "commonVersion" }
compose-shimmer = { module = "com.valentinilk.shimmer:compose-shimmer", version.ref = "composeShimmer" }
composeneumorphism = { module = "me.nikhilchaudhari:composeNeumorphism", version.ref = "composeneumorphism" }
dexkit = { module = "org.luckypray:dexkit", version.ref = "dexkitVersion" }
drawabletoolbox = { module = "com.github.duanhong169:drawabletoolbox", version.ref = "drawabletoolboxVersion" }
expandablebottombar = { module = "com.github.st235:expandablebottombar", version.ref = "expandablebottombar" }
ezxhelper = { module = "com.github.kyuubiran:EzXHelper", version.ref = "ezxhelper" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
haze = { module = "dev.chrisbanes.haze:haze", version.ref = "haze" }
junit = { module = "junit:junit", version.ref = "junit" }
ksp-xposed = { module = "com.highcapable.yukihookapi:ksp-xposed", version.ref = "kspXposedVersion" }
lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "lottieComposeVersion" }
material = { module = "com.google.android.material:material", version.ref = "materialVersion" }
material3 = { module = "androidx.compose.material3:material3" }
miuix = { module = "top.yukonga.miuix.kmp:miuix-android", version.ref = "miuix" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttpVersion" }
toolbar-compose = { module = "me.onebone:toolbar-compose", version.ref = "toolbarComposeVersion" }
ui = { module = "androidx.compose.ui:ui" }
ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4" }
ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
umsdk-uyumao = { module = "com.umeng.umsdk:uyumao", version.ref = "uyumao" }
umsdk-asms = { module = "com.umeng.umsdk:asms", version.ref = "asms" }
xxpermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermissionsVersion" }
yukihookapi-api = { module = "com.highcapable.yukihookapi:api", version.ref = "api" }

[plugins]
