<div align="center">

# O神 - ColorOS辅助模块

<img src="https://github.com/suqi8/OPatch/blob/e9cb9e2af53b728e5e2d2d00429a90f10a3384dc/app/src/main/ic_launcher1-playstore.png?raw=true" width="160" height="160" style="display: block; margin: 0 auto;" alt="icon">

### 简体中文 | [English](./README_EN.md) | [文档](https://oshin.mikusignal.top/)

![OShin](https://socialify.git.ci/suqi8/OPatch/image?font=Rokkitt&forks=1&issues=1&language=1&name=1&owner=1&pattern=Brick%20Wall&pulls=1&stargazers=1&theme=Auto)

</div>

## 简介

原名：OPatch  
O神是一个专为ColorOS系统设计的辅助模块，旨在增强和定制用户的操作系统体验。该模块允许用户轻松地应用补丁，以实现特定的功能或优化。

## 官方渠道

我们为您提供了多个官方渠道，您可以通过这些渠道与我们联系或获取最新信息：

### Telegram

- **[O神 官方频道](https://t.me/OPatchA)**
- **[O神 聊天频道](https://t.me/OPatchB)**
- **[O神 自动构建](https://t.me/OPatchC)**

### QQ

- **[O神 QQ群](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=dbP78P2qCYuR2RxGtwmwCrlMCsh2MB2N&authKey=uTkJAGf0gg7%2Fx%2B3OBPrf%2F%2FnyZY2ntPNvnz6%2BTUo%2BHa0Pe%2F%2FqtXvK%2BSJ3%2B4PS0zbO&noverify=0&group_code=740266099)**

## 支持的版本
目前仅支持基于 Android 15 的 ColorOS 15 / RealmeUI 6.0 / OxygenOS 15

## 名称的由来
~~仓库的名称"OPatch"来源于"OPPO OnePlus Patch"，主要应用场景是OPPO和OnePlus设备上的ColorOS系统。~~  
经过各位的讨论与投票后，我们决定将名称改为 O神。

## 贡献
如果您想为我的模块加入您使用的语言，请前往 [Crowdin](https://zh.crowdin.com/project/opatch) 页面进行贡献。

> [!CAUTION]
>
> 该应用需要 Magisk、KernelSU 和 APatch 来获取 root 权限，并且需要 XPosed/LSPosed 框架。任何替代方法都将无效。

## 许可
本项目采用[AGPL许可证](LICENSE)，详情请参阅LICENSE文件。

## 联系我们
如果您有任何问题或需要帮助，请通过以下方式联系我们：
- GitHub Issues
- 电子邮件：[<EMAIL>]

---

## 贡献者

感谢以下贡献者

<a href="https://github.com/suqi8/OPatch/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=suqi8/OPatch"  alt=""/>
</a>


![Alt](https://repobeats.axiom.co/api/embed/cc78ce423b8c1fe1ca2dfdc4ce580bc1fa8bfd62.svg "Repobeats analytics image")

[![Star History Chart](https://api.star-history.com/svg?repos=suqi8/OPatch&type=Date)](https://star-history.com/#suqi8/OPatch&Date)

