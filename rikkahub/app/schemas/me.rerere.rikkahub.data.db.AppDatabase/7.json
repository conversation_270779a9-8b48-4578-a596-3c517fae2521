{"formatVersion": 1, "database": {"version": 7, "identityHash": "c539c3b756c32c524f53324e31139dea", "entities": [{"tableName": "ConversationEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `assistant_id` TEXT NOT NULL DEFAULT '0950e2dc-9bd5-4801-afa3-aa887aa36b4e', `title` TEXT NOT NULL, `nodes` TEXT NOT NULL, `usage` TEXT, `create_at` INTEGER NOT NULL, `update_at` INTEGER NOT NULL, `truncate_index` INTEGER NOT NULL DEFAULT -1, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "assistantId", "columnName": "assistant_id", "affinity": "TEXT", "notNull": true, "defaultValue": "'0950e2dc-9bd5-4801-afa3-aa887aa36b4e'"}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nodes", "columnName": "nodes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tokenUsage", "columnName": "usage", "affinity": "TEXT"}, {"fieldPath": "createAt", "columnName": "create_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateAt", "columnName": "update_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "truncateIndex", "columnName": "truncate_index", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "MemoryEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `assistant_id` TEXT NOT NULL, `content` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "assistantId", "columnName": "assistant_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c539c3b756c32c524f53324e31139dea')"]}}