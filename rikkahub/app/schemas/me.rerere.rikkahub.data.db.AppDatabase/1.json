{"formatVersion": 1, "database": {"version": 1, "identityHash": "bc245d02319084011973fc91bd61fe13", "entities": [{"tableName": "ConversationEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `messages` TEXT NOT NULL, `create_at` INTEGER NOT NULL, `update_at` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "messages", "columnName": "messages", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createAt", "columnName": "create_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateAt", "columnName": "update_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'bc245d02319084011973fc91bd61fe13')"]}}