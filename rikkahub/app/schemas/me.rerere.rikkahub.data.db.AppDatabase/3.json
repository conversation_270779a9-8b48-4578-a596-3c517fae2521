{"formatVersion": 1, "database": {"version": 3, "identityHash": "76a2f34ca8eea9c4298b4d4f554a3589", "entities": [{"tableName": "ConversationEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `messages` TEXT NOT NULL, `create_at` INTEGER NOT NULL, `update_at` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "messages", "columnName": "messages", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createAt", "columnName": "create_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateAt", "columnName": "update_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "MemoryEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `assistant_id` TEXT NOT NULL, `content` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "assistantId", "columnName": "assistant_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '76a2f34ca8eea9c4298b4d4f554a3589')"]}}