package me.rerere.rikkahub.ui.theme.presets

import androidx.compose.material3.Text
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import me.rerere.rikkahub.R
import me.rerere.rikkahub.ui.theme.PresetTheme

val SakuraThemePreset by lazy {
    PresetTheme(
        id = "sakura",
        name = {
            Text(stringResource(id = R.string.theme_name_sakura))
        },
        standardLight = lightScheme,
        standardDark = darkScheme,
        mediumContrastLight = mediumContrastLightColorScheme,
        mediumContrastDark = mediumContrastDarkColorScheme,
        highContrastLight = highContrastLightColorScheme,
        highContrastDark = highContrastDarkColorScheme,
    )
}

//region Sakura Theme Colors
private val primaryLight = Color(0xFF8E4955)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFFFD9DD)
private val onPrimaryContainerLight = Color(0xFF72333E)
private val secondaryLight = Color(0xFF76565A)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFFD9DD)
private val onSecondaryContainerLight = Color(0xFF5C3F43)
private val tertiaryLight = Color(0xFF785831)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFFFDDB8)
private val onTertiaryContainerLight = Color(0xFF5E411C)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF93000A)
private val backgroundLight = Color(0xFFFFF8F7)
private val onBackgroundLight = Color(0xFF22191A)
private val surfaceLight = Color(0xFFFFF8F7)
private val onSurfaceLight = Color(0xFF22191A)
private val surfaceVariantLight = Color(0xFFF3DDDF)
private val onSurfaceVariantLight = Color(0xFF524345)
private val outlineLight = Color(0xFF847374)
private val outlineVariantLight = Color(0xFFD7C1C3)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF382E2F)
private val inverseOnSurfaceLight = Color(0xFFFEEDED)
private val inversePrimaryLight = Color(0xFFFFB2BC)
private val surfaceDimLight = Color(0xFFE7D6D7)
private val surfaceBrightLight = Color(0xFFFFF8F7)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFFF0F1)
private val surfaceContainerLight = Color(0xFFFBEAEB)
private val surfaceContainerHighLight = Color(0xFFF6E4E5)
private val surfaceContainerHighestLight = Color(0xFFF0DEDF)

private val primaryLightMediumContrast = Color(0xFF5D222E)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFFA05863)
private val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryLightMediumContrast = Color(0xFF4A2F32)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF866568)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF4B310C)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF89673E)
private val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val errorLightMediumContrast = Color(0xFF740006)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFCF2C27)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFFF8F7)
private val onBackgroundLightMediumContrast = Color(0xFF22191A)
private val surfaceLightMediumContrast = Color(0xFFFFF8F7)
private val onSurfaceLightMediumContrast = Color(0xFF170F10)
private val surfaceVariantLightMediumContrast = Color(0xFFF3DDDF)
private val onSurfaceVariantLightMediumContrast = Color(0xFF403334)
private val outlineLightMediumContrast = Color(0xFF5E4F50)
private val outlineVariantLightMediumContrast = Color(0xFF7A696A)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF382E2F)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFFEEDED)
private val inversePrimaryLightMediumContrast = Color(0xFFFFB2BC)
private val surfaceDimLightMediumContrast = Color(0xFFD3C3C4)
private val surfaceBrightLightMediumContrast = Color(0xFFFFF8F7)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFFFF0F1)
private val surfaceContainerLightMediumContrast = Color(0xFFF6E4E5)
private val surfaceContainerHighLightMediumContrast = Color(0xFFEAD9DA)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFDECECF)

private val primaryLightHighContrast = Color(0xFF501824)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF753540)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF3F2529)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF5F4145)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF402704)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF61431E)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF600004)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF98000A)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFFF8F7)
private val onBackgroundLightHighContrast = Color(0xFF22191A)
private val surfaceLightHighContrast = Color(0xFFFFF8F7)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFF3DDDF)
private val onSurfaceVariantLightHighContrast = Color(0xFF000000)
private val outlineLightHighContrast = Color(0xFF36292A)
private val outlineVariantLightHighContrast = Color(0xFF544547)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF382E2F)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFFFB2BC)
private val surfaceDimLightHighContrast = Color(0xFFC5B5B6)
private val surfaceBrightLightHighContrast = Color(0xFFFFF8F7)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFFEEDED)
private val surfaceContainerLightHighContrast = Color(0xFFF0DEDF)
private val surfaceContainerHighLightHighContrast = Color(0xFFE1D0D1)
private val surfaceContainerHighestLightHighContrast = Color(0xFFD3C3C4)

private val primaryDark = Color(0xFFFFB2BC)
private val onPrimaryDark = Color(0xFF561D28)
private val primaryContainerDark = Color(0xFF72333E)
private val onPrimaryContainerDark = Color(0xFFFFD9DD)
private val secondaryDark = Color(0xFFE5BDC1)
private val onSecondaryDark = Color(0xFF43292D)
private val secondaryContainerDark = Color(0xFF5C3F43)
private val onSecondaryContainerDark = Color(0xFFFFD9DD)
private val tertiaryDark = Color(0xFFEABF8F)
private val onTertiaryDark = Color(0xFF452B07)
private val tertiaryContainerDark = Color(0xFF5E411C)
private val onTertiaryContainerDark = Color(0xFFFFDDB8)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF1A1112)
private val onBackgroundDark = Color(0xFFF0DEDF)
private val surfaceDark = Color(0xFF1A1112)
private val onSurfaceDark = Color(0xFFF0DEDF)
private val surfaceVariantDark = Color(0xFF524345)
private val onSurfaceVariantDark = Color(0xFFD7C1C3)
private val outlineDark = Color(0xFF9F8C8E)
private val outlineVariantDark = Color(0xFF524345)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFF0DEDF)
private val inverseOnSurfaceDark = Color(0xFF382E2F)
private val inversePrimaryDark = Color(0xFF8E4955)
private val surfaceDimDark = Color(0xFF1A1112)
private val surfaceBrightDark = Color(0xFF413738)
private val surfaceContainerLowestDark = Color(0xFF140C0D)
private val surfaceContainerLowDark = Color(0xFF22191A)
private val surfaceContainerDark = Color(0xFF261D1E)
private val surfaceContainerHighDark = Color(0xFF312828)
private val surfaceContainerHighestDark = Color(0xFF3D3233)

private val primaryDarkMediumContrast = Color(0xFFFFD1D6)
private val onPrimaryDarkMediumContrast = Color(0xFF48121E)
private val primaryContainerDarkMediumContrast = Color(0xFFC97A86)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFFCD2D6)
private val onSecondaryDarkMediumContrast = Color(0xFF371F22)
private val secondaryContainerDarkMediumContrast = Color(0xFFAC888C)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFFFD5A7)
private val onTertiaryDarkMediumContrast = Color(0xFF382000)
private val tertiaryContainerDarkMediumContrast = Color(0xFFB08A5E)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFD2CC)
private val onErrorDarkMediumContrast = Color(0xFF540003)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF1A1112)
private val onBackgroundDarkMediumContrast = Color(0xFFF0DEDF)
private val surfaceDarkMediumContrast = Color(0xFF1A1112)
private val onSurfaceDarkMediumContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkMediumContrast = Color(0xFF524345)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFEDD7D9)
private val outlineDarkMediumContrast = Color(0xFFC1ADAF)
private val outlineVariantDarkMediumContrast = Color(0xFF9F8C8D)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFF0DEDF)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF312829)
private val inversePrimaryDarkMediumContrast = Color(0xFF73343F)
private val surfaceDimDarkMediumContrast = Color(0xFF1A1112)
private val surfaceBrightDarkMediumContrast = Color(0xFF4D4243)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF0C0607)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF241B1C)
private val surfaceContainerDarkMediumContrast = Color(0xFF2F2526)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF3A3031)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF463B3C)

private val primaryDarkHighContrast = Color(0xFFFFEBEC)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFFFACB7)
private val onPrimaryContainerDarkHighContrast = Color(0xFF210006)
private val secondaryDarkHighContrast = Color(0xFFFFEBEC)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFE1B9BD)
private val onSecondaryContainerDarkHighContrast = Color(0xFF190608)
private val tertiaryDarkHighContrast = Color(0xFFFFEDDC)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFE5BB8B)
private val onTertiaryContainerDarkHighContrast = Color(0xFF140900)
private val errorDarkHighContrast = Color(0xFFFFECE9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFAEA4)
private val onErrorContainerDarkHighContrast = Color(0xFF220001)
private val backgroundDarkHighContrast = Color(0xFF1A1112)
private val onBackgroundDarkHighContrast = Color(0xFFF0DEDF)
private val surfaceDarkHighContrast = Color(0xFF1A1112)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF524345)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFFFFF)
private val outlineDarkHighContrast = Color(0xFFFFEBEC)
private val outlineVariantDarkHighContrast = Color(0xFFD3BEBF)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFF0DEDF)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF73343F)
private val surfaceDimDarkHighContrast = Color(0xFF1A1112)
private val surfaceBrightDarkHighContrast = Color(0xFF594D4E)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF000000)
private val surfaceContainerLowDarkHighContrast = Color(0xFF261D1E)
private val surfaceContainerDarkHighContrast = Color(0xFF382E2F)
private val surfaceContainerHighDarkHighContrast = Color(0xFF43393A)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF4F4445)
//endregion

// region Sakura Schemes
private val lightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

private val darkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)

private val mediumContrastLightColorScheme = lightColorScheme(
    primary = primaryLightMediumContrast,
    onPrimary = onPrimaryLightMediumContrast,
    primaryContainer = primaryContainerLightMediumContrast,
    onPrimaryContainer = onPrimaryContainerLightMediumContrast,
    secondary = secondaryLightMediumContrast,
    onSecondary = onSecondaryLightMediumContrast,
    secondaryContainer = secondaryContainerLightMediumContrast,
    onSecondaryContainer = onSecondaryContainerLightMediumContrast,
    tertiary = tertiaryLightMediumContrast,
    onTertiary = onTertiaryLightMediumContrast,
    tertiaryContainer = tertiaryContainerLightMediumContrast,
    onTertiaryContainer = onTertiaryContainerLightMediumContrast,
    error = errorLightMediumContrast,
    onError = onErrorLightMediumContrast,
    errorContainer = errorContainerLightMediumContrast,
    onErrorContainer = onErrorContainerLightMediumContrast,
    background = backgroundLightMediumContrast,
    onBackground = onBackgroundLightMediumContrast,
    surface = surfaceLightMediumContrast,
    onSurface = onSurfaceLightMediumContrast,
    surfaceVariant = surfaceVariantLightMediumContrast,
    onSurfaceVariant = onSurfaceVariantLightMediumContrast,
    outline = outlineLightMediumContrast,
    outlineVariant = outlineVariantLightMediumContrast,
    scrim = scrimLightMediumContrast,
    inverseSurface = inverseSurfaceLightMediumContrast,
    inverseOnSurface = inverseOnSurfaceLightMediumContrast,
    inversePrimary = inversePrimaryLightMediumContrast,
    surfaceDim = surfaceDimLightMediumContrast,
    surfaceBright = surfaceBrightLightMediumContrast,
    surfaceContainerLowest = surfaceContainerLowestLightMediumContrast,
    surfaceContainerLow = surfaceContainerLowLightMediumContrast,
    surfaceContainer = surfaceContainerLightMediumContrast,
    surfaceContainerHigh = surfaceContainerHighLightMediumContrast,
    surfaceContainerHighest = surfaceContainerHighestLightMediumContrast,
)

private val highContrastLightColorScheme = lightColorScheme(
    primary = primaryLightHighContrast,
    onPrimary = onPrimaryLightHighContrast,
    primaryContainer = primaryContainerLightHighContrast,
    onPrimaryContainer = onPrimaryContainerLightHighContrast,
    secondary = secondaryLightHighContrast,
    onSecondary = onSecondaryLightHighContrast,
    secondaryContainer = secondaryContainerLightHighContrast,
    onSecondaryContainer = onSecondaryContainerLightHighContrast,
    tertiary = tertiaryLightHighContrast,
    onTertiary = onTertiaryLightHighContrast,
    tertiaryContainer = tertiaryContainerLightHighContrast,
    onTertiaryContainer = onTertiaryContainerLightHighContrast,
    error = errorLightHighContrast,
    onError = onErrorLightHighContrast,
    errorContainer = errorContainerLightHighContrast,
    onErrorContainer = onErrorContainerLightHighContrast,
    background = backgroundLightHighContrast,
    onBackground = onBackgroundLightHighContrast,
    surface = surfaceLightHighContrast,
    onSurface = onSurfaceLightHighContrast,
    surfaceVariant = surfaceVariantLightHighContrast,
    onSurfaceVariant = onSurfaceVariantLightHighContrast,
    outline = outlineLightHighContrast,
    outlineVariant = outlineVariantLightHighContrast,
    scrim = scrimLightHighContrast,
    inverseSurface = inverseSurfaceLightHighContrast,
    inverseOnSurface = inverseOnSurfaceLightHighContrast,
    inversePrimary = inversePrimaryLightHighContrast,
    surfaceDim = surfaceDimLightHighContrast,
    surfaceBright = surfaceBrightLightHighContrast,
    surfaceContainerLowest = surfaceContainerLowestLightHighContrast,
    surfaceContainerLow = surfaceContainerLowLightHighContrast,
    surfaceContainer = surfaceContainerLightHighContrast,
    surfaceContainerHigh = surfaceContainerHighLightHighContrast,
    surfaceContainerHighest = surfaceContainerHighestLightHighContrast,
)

private val mediumContrastDarkColorScheme = darkColorScheme(
    primary = primaryDarkMediumContrast,
    onPrimary = onPrimaryDarkMediumContrast,
    primaryContainer = primaryContainerDarkMediumContrast,
    onPrimaryContainer = onPrimaryContainerDarkMediumContrast,
    secondary = secondaryDarkMediumContrast,
    onSecondary = onSecondaryDarkMediumContrast,
    secondaryContainer = secondaryContainerDarkMediumContrast,
    onSecondaryContainer = onSecondaryContainerDarkMediumContrast,
    tertiary = tertiaryDarkMediumContrast,
    onTertiary = onTertiaryDarkMediumContrast,
    tertiaryContainer = tertiaryContainerDarkMediumContrast,
    onTertiaryContainer = onTertiaryContainerDarkMediumContrast,
    error = errorDarkMediumContrast,
    onError = onErrorDarkMediumContrast,
    errorContainer = errorContainerDarkMediumContrast,
    onErrorContainer = onErrorContainerDarkMediumContrast,
    background = backgroundDarkMediumContrast,
    onBackground = onBackgroundDarkMediumContrast,
    surface = surfaceDarkMediumContrast,
    onSurface = onSurfaceDarkMediumContrast,
    surfaceVariant = surfaceVariantDarkMediumContrast,
    onSurfaceVariant = onSurfaceVariantDarkMediumContrast,
    outline = outlineDarkMediumContrast,
    outlineVariant = outlineVariantDarkMediumContrast,
    scrim = scrimDarkMediumContrast,
    inverseSurface = inverseSurfaceDarkMediumContrast,
    inverseOnSurface = inverseOnSurfaceDarkMediumContrast,
    inversePrimary = inversePrimaryDarkMediumContrast,
    surfaceDim = surfaceDimDarkMediumContrast,
    surfaceBright = surfaceBrightDarkMediumContrast,
    surfaceContainerLowest = surfaceContainerLowestDarkMediumContrast,
    surfaceContainerLow = surfaceContainerLowDarkMediumContrast,
    surfaceContainer = surfaceContainerDarkMediumContrast,
    surfaceContainerHigh = surfaceContainerHighDarkMediumContrast,
    surfaceContainerHighest = surfaceContainerHighestDarkMediumContrast,
)

private val highContrastDarkColorScheme = darkColorScheme(
    primary = primaryDarkHighContrast,
    onPrimary = onPrimaryDarkHighContrast,
    primaryContainer = primaryContainerDarkHighContrast,
    onPrimaryContainer = onPrimaryContainerDarkHighContrast,
    secondary = secondaryDarkHighContrast,
    onSecondary = onSecondaryDarkHighContrast,
    secondaryContainer = secondaryContainerDarkHighContrast,
    onSecondaryContainer = onSecondaryContainerDarkHighContrast,
    tertiary = tertiaryDarkHighContrast,
    onTertiary = onTertiaryDarkHighContrast,
    tertiaryContainer = tertiaryContainerDarkHighContrast,
    onTertiaryContainer = onTertiaryContainerDarkHighContrast,
    error = errorDarkHighContrast,
    onError = onErrorDarkHighContrast,
    errorContainer = errorContainerDarkHighContrast,
    onErrorContainer = onErrorContainerDarkHighContrast,
    background = backgroundDarkHighContrast,
    onBackground = onBackgroundDarkHighContrast,
    surface = surfaceDarkHighContrast,
    onSurface = onSurfaceDarkHighContrast,
    surfaceVariant = surfaceVariantDarkHighContrast,
    onSurfaceVariant = onSurfaceVariantDarkHighContrast,
    outline = outlineDarkHighContrast,
    outlineVariant = outlineVariantDarkHighContrast,
    scrim = scrimDarkHighContrast,
    inverseSurface = inverseSurfaceDarkHighContrast,
    inverseOnSurface = inverseOnSurfaceDarkHighContrast,
    inversePrimary = inversePrimaryDarkHighContrast,
    surfaceDim = surfaceDimDarkHighContrast,
    surfaceBright = surfaceBrightDarkHighContrast,
    surfaceContainerLowest = surfaceContainerLowestDarkHighContrast,
    surfaceContainerLow = surfaceContainerLowDarkHighContrast,
    surfaceContainer = surfaceContainerDarkHighContrast,
    surfaceContainerHigh = surfaceContainerHighDarkHighContrast,
    surfaceContainerHighest = surfaceContainerHighestDarkHighContrast,
)
// endregion
