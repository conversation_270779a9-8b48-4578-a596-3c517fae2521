package me.rerere.rikkahub.ui.theme.presets

import androidx.compose.material3.Text
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import me.rerere.rikkahub.R
import me.rerere.rikkahub.ui.theme.PresetTheme

val BlackThemePreset by lazy {
    PresetTheme(
        id = "black",
        name = {
            Text(stringResource(id = R.string.theme_name_black))
        },
        standardLight = lightScheme,
        standardDark = darkScheme,
        mediumContrastLight = mediumContrastLightColorScheme,
        mediumContrastDark = mediumContrastDarkColorScheme,
        highContrastLight = highContrastLightColorScheme,
        highContrastDark = highContrastDarkColorScheme,
    )
}

private val primaryLight = Color(0xFF2B2C2D)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFF414243)
private val onPrimaryContainerLight = Color(0xFFAEAEAF)
private val secondaryLight = Color(0xFF5F5E5E)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFE4E2E2)
private val onSecondaryContainerLight = Color(0xFF656464)
private val tertiaryLight = Color(0xFF2D2B2D)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFF434143)
private val onTertiaryContainerLight = Color(0xFFB1ADAF)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF93000A)
private val backgroundLight = Color(0xFFFCF8F8)
private val onBackgroundLight = Color(0xFF1C1B1B)
private val surfaceLight = Color(0xFFFCF8F8)
private val onSurfaceLight = Color(0xFF1C1B1B)
private val surfaceVariantLight = Color(0xFFE1E3E5)
private val onSurfaceVariantLight = Color(0xFF444749)
private val outlineLight = Color(0xFF75777A)
private val outlineVariantLight = Color(0xFFC5C7C9)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF313030)
private val inverseOnSurfaceLight = Color(0xFFF4F0EF)
private val inversePrimaryLight = Color(0xFFC7C6C7)
private val surfaceDimLight = Color(0xFFDDD9D9)
private val surfaceBrightLight = Color(0xFFFCF8F8)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFF7F3F2)
private val surfaceContainerLight = Color(0xFFF1EDEC)
private val surfaceContainerHighLight = Color(0xFFEBE7E7)
private val surfaceContainerHighestLight = Color(0xFFE5E2E1)

private val primaryLightMediumContrast = Color(0xFF2B2C2D)
private val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
private val primaryContainerLightMediumContrast = Color(0xFF414243)
private val onPrimaryContainerLightMediumContrast = Color(0xFFD9D8D9)
private val secondaryLightMediumContrast = Color(0xFF363636)
private val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightMediumContrast = Color(0xFF6E6D6D)
private val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryLightMediumContrast = Color(0xFF2D2B2D)
private val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightMediumContrast = Color(0xFF434143)
private val onTertiaryContainerLightMediumContrast = Color(0xFFDBD7D9)
private val errorLightMediumContrast = Color(0xFF740006)
private val onErrorLightMediumContrast = Color(0xFFFFFFFF)
private val errorContainerLightMediumContrast = Color(0xFFCF2C27)
private val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
private val backgroundLightMediumContrast = Color(0xFFFCF8F8)
private val onBackgroundLightMediumContrast = Color(0xFF1C1B1B)
private val surfaceLightMediumContrast = Color(0xFFFCF8F8)
private val onSurfaceLightMediumContrast = Color(0xFF111111)
private val surfaceVariantLightMediumContrast = Color(0xFFE1E3E5)
private val onSurfaceVariantLightMediumContrast = Color(0xFF333739)
private val outlineLightMediumContrast = Color(0xFF505355)
private val outlineVariantLightMediumContrast = Color(0xFF6B6D70)
private val scrimLightMediumContrast = Color(0xFF000000)
private val inverseSurfaceLightMediumContrast = Color(0xFF313030)
private val inverseOnSurfaceLightMediumContrast = Color(0xFFF4F0EF)
private val inversePrimaryLightMediumContrast = Color(0xFFC7C6C7)
private val surfaceDimLightMediumContrast = Color(0xFFC9C6C5)
private val surfaceBrightLightMediumContrast = Color(0xFFFCF8F8)
private val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightMediumContrast = Color(0xFFF7F3F2)
private val surfaceContainerLightMediumContrast = Color(0xFFEBE7E7)
private val surfaceContainerHighLightMediumContrast = Color(0xFFDFDCDB)
private val surfaceContainerHighestLightMediumContrast = Color(0xFFD4D1D0)

private val primaryLightHighContrast = Color(0xFF2B2C2D)
private val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
private val primaryContainerLightHighContrast = Color(0xFF414243)
private val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val secondaryLightHighContrast = Color(0xFF2C2C2C)
private val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
private val secondaryContainerLightHighContrast = Color(0xFF494949)
private val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryLightHighContrast = Color(0xFF2D2B2D)
private val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
private val tertiaryContainerLightHighContrast = Color(0xFF434143)
private val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
private val errorLightHighContrast = Color(0xFF600004)
private val onErrorLightHighContrast = Color(0xFFFFFFFF)
private val errorContainerLightHighContrast = Color(0xFF98000A)
private val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
private val backgroundLightHighContrast = Color(0xFFFCF8F8)
private val onBackgroundLightHighContrast = Color(0xFF1C1B1B)
private val surfaceLightHighContrast = Color(0xFFFCF8F8)
private val onSurfaceLightHighContrast = Color(0xFF000000)
private val surfaceVariantLightHighContrast = Color(0xFFE1E3E5)
private val onSurfaceVariantLightHighContrast = Color(0xFF000000)
private val outlineLightHighContrast = Color(0xFF292D2E)
private val outlineVariantLightHighContrast = Color(0xFF47494C)
private val scrimLightHighContrast = Color(0xFF000000)
private val inverseSurfaceLightHighContrast = Color(0xFF313030)
private val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
private val inversePrimaryLightHighContrast = Color(0xFFC7C6C7)
private val surfaceDimLightHighContrast = Color(0xFFBBB8B8)
private val surfaceBrightLightHighContrast = Color(0xFFFCF8F8)
private val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
private val surfaceContainerLowLightHighContrast = Color(0xFFF4F0EF)
private val surfaceContainerLightHighContrast = Color(0xFFE5E2E1)
private val surfaceContainerHighLightHighContrast = Color(0xFFD7D4D3)
private val surfaceContainerHighestLightHighContrast = Color(0xFFC9C6C5)

private val primaryDark = Color(0xFFC7C6C7)
private val onPrimaryDark = Color(0xFF2F3031)
private val primaryContainerDark = Color(0xFF414243)
private val onPrimaryContainerDark = Color(0xFFAEAEAF)
private val secondaryDark = Color(0xFFC8C6C6)
private val onSecondaryDark = Color(0xFF303031)
private val secondaryContainerDark = Color(0xFF474747)
private val onSecondaryContainerDark = Color(0xFFB6B5B5)
private val tertiaryDark = Color(0xFFCAC5C7)
private val onTertiaryDark = Color(0xFF313032)
private val tertiaryContainerDark = Color(0xFF434143)
private val onTertiaryContainerDark = Color(0xFFB1ADAF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF141313)
private val onBackgroundDark = Color(0xFFE5E2E1)
private val surfaceDark = Color(0xFF141313)
private val onSurfaceDark = Color(0xFFE5E2E1)
private val surfaceVariantDark = Color(0xFF444749)
private val onSurfaceVariantDark = Color(0xFFC5C7C9)
private val outlineDark = Color(0xFF8E9193)
private val outlineVariantDark = Color(0xFF444749)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFE5E2E1)
private val inverseOnSurfaceDark = Color(0xFF313030)
private val inversePrimaryDark = Color(0xFF5E5E5F)
private val surfaceDimDark = Color(0xFF141313)
private val surfaceBrightDark = Color(0xFF3A3939)
private val surfaceContainerLowestDark = Color(0xFF0E0E0E)
private val surfaceContainerLowDark = Color(0xFF1C1B1B)
private val surfaceContainerDark = Color(0xFF201F1F)
private val surfaceContainerHighDark = Color(0xFF2A2A2A)
private val surfaceContainerHighestDark = Color(0xFF353434)

private val primaryDarkMediumContrast = Color(0xFFDDDCDD)
private val onPrimaryDarkMediumContrast = Color(0xFF242627)
private val primaryContainerDarkMediumContrast = Color(0xFF909192)
private val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
private val secondaryDarkMediumContrast = Color(0xFFDEDCDC)
private val onSecondaryDarkMediumContrast = Color(0xFF252626)
private val secondaryContainerDarkMediumContrast = Color(0xFF929090)
private val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
private val tertiaryDarkMediumContrast = Color(0xFFE0DBDD)
private val onTertiaryDarkMediumContrast = Color(0xFF272527)
private val tertiaryContainerDarkMediumContrast = Color(0xFF939092)
private val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
private val errorDarkMediumContrast = Color(0xFFFFD2CC)
private val onErrorDarkMediumContrast = Color(0xFF540003)
private val errorContainerDarkMediumContrast = Color(0xFFFF5449)
private val onErrorContainerDarkMediumContrast = Color(0xFF000000)
private val backgroundDarkMediumContrast = Color(0xFF141313)
private val onBackgroundDarkMediumContrast = Color(0xFFE5E2E1)
private val surfaceDarkMediumContrast = Color(0xFF141313)
private val onSurfaceDarkMediumContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkMediumContrast = Color(0xFF444749)
private val onSurfaceVariantDarkMediumContrast = Color(0xFFDBDCDF)
private val outlineDarkMediumContrast = Color(0xFFB0B2B4)
private val outlineVariantDarkMediumContrast = Color(0xFF8E9193)
private val scrimDarkMediumContrast = Color(0xFF000000)
private val inverseSurfaceDarkMediumContrast = Color(0xFFE5E2E1)
private val inverseOnSurfaceDarkMediumContrast = Color(0xFF2A2A2A)
private val inversePrimaryDarkMediumContrast = Color(0xFF474849)
private val surfaceDimDarkMediumContrast = Color(0xFF141313)
private val surfaceBrightDarkMediumContrast = Color(0xFF454444)
private val surfaceContainerLowestDarkMediumContrast = Color(0xFF070707)
private val surfaceContainerLowDarkMediumContrast = Color(0xFF1E1D1D)
private val surfaceContainerDarkMediumContrast = Color(0xFF282828)
private val surfaceContainerHighDarkMediumContrast = Color(0xFF333232)
private val surfaceContainerHighestDarkMediumContrast = Color(0xFF3E3D3D)

private val primaryDarkHighContrast = Color(0xFFF1F0F0)
private val onPrimaryDarkHighContrast = Color(0xFF000000)
private val primaryContainerDarkHighContrast = Color(0xFFC3C2C3)
private val onPrimaryContainerDarkHighContrast = Color(0xFF0A0B0C)
private val secondaryDarkHighContrast = Color(0xFFF2EFEF)
private val onSecondaryDarkHighContrast = Color(0xFF000000)
private val secondaryContainerDarkHighContrast = Color(0xFFC4C2C2)
private val onSecondaryContainerDarkHighContrast = Color(0xFF0B0B0C)
private val tertiaryDarkHighContrast = Color(0xFFF4EFF1)
private val onTertiaryDarkHighContrast = Color(0xFF000000)
private val tertiaryContainerDarkHighContrast = Color(0xFFC6C1C4)
private val onTertiaryContainerDarkHighContrast = Color(0xFF0C0B0D)
private val errorDarkHighContrast = Color(0xFFFFECE9)
private val onErrorDarkHighContrast = Color(0xFF000000)
private val errorContainerDarkHighContrast = Color(0xFFFFAEA4)
private val onErrorContainerDarkHighContrast = Color(0xFF220001)
private val backgroundDarkHighContrast = Color(0xFF141313)
private val onBackgroundDarkHighContrast = Color(0xFFE5E2E1)
private val surfaceDarkHighContrast = Color(0xFF141313)
private val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
private val surfaceVariantDarkHighContrast = Color(0xFF444749)
private val onSurfaceVariantDarkHighContrast = Color(0xFFFFFFFF)
private val outlineDarkHighContrast = Color(0xFFEEF0F2)
private val outlineVariantDarkHighContrast = Color(0xFFC1C3C5)
private val scrimDarkHighContrast = Color(0xFF000000)
private val inverseSurfaceDarkHighContrast = Color(0xFFE5E2E1)
private val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
private val inversePrimaryDarkHighContrast = Color(0xFF474849)
private val surfaceDimDarkHighContrast = Color(0xFF141313)
private val surfaceBrightDarkHighContrast = Color(0xFF515050)
private val surfaceContainerLowestDarkHighContrast = Color(0xFF000000)
private val surfaceContainerLowDarkHighContrast = Color(0xFF201F1F)
private val surfaceContainerDarkHighContrast = Color(0xFF313030)
private val surfaceContainerHighDarkHighContrast = Color(0xFF3C3B3B)
private val surfaceContainerHighestDarkHighContrast = Color(0xFF484646)

private val lightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

private val darkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)

private val mediumContrastLightColorScheme = lightColorScheme(
    primary = primaryLightMediumContrast,
    onPrimary = onPrimaryLightMediumContrast,
    primaryContainer = primaryContainerLightMediumContrast,
    onPrimaryContainer = onPrimaryContainerLightMediumContrast,
    secondary = secondaryLightMediumContrast,
    onSecondary = onSecondaryLightMediumContrast,
    secondaryContainer = secondaryContainerLightMediumContrast,
    onSecondaryContainer = onSecondaryContainerLightMediumContrast,
    tertiary = tertiaryLightMediumContrast,
    onTertiary = onTertiaryLightMediumContrast,
    tertiaryContainer = tertiaryContainerLightMediumContrast,
    onTertiaryContainer = onTertiaryContainerLightMediumContrast,
    error = errorLightMediumContrast,
    onError = onErrorLightMediumContrast,
    errorContainer = errorContainerLightMediumContrast,
    onErrorContainer = onErrorContainerLightMediumContrast,
    background = backgroundLightMediumContrast,
    onBackground = onBackgroundLightMediumContrast,
    surface = surfaceLightMediumContrast,
    onSurface = onSurfaceLightMediumContrast,
    surfaceVariant = surfaceVariantLightMediumContrast,
    onSurfaceVariant = onSurfaceVariantLightMediumContrast,
    outline = outlineLightMediumContrast,
    outlineVariant = outlineVariantLightMediumContrast,
    scrim = scrimLightMediumContrast,
    inverseSurface = inverseSurfaceLightMediumContrast,
    inverseOnSurface = inverseOnSurfaceLightMediumContrast,
    inversePrimary = inversePrimaryLightMediumContrast,
    surfaceDim = surfaceDimLightMediumContrast,
    surfaceBright = surfaceBrightLightMediumContrast,
    surfaceContainerLowest = surfaceContainerLowestLightMediumContrast,
    surfaceContainerLow = surfaceContainerLowLightMediumContrast,
    surfaceContainer = surfaceContainerLightMediumContrast,
    surfaceContainerHigh = surfaceContainerHighLightMediumContrast,
    surfaceContainerHighest = surfaceContainerHighestLightMediumContrast,
)

private val highContrastLightColorScheme = lightColorScheme(
    primary = primaryLightHighContrast,
    onPrimary = onPrimaryLightHighContrast,
    primaryContainer = primaryContainerLightHighContrast,
    onPrimaryContainer = onPrimaryContainerLightHighContrast,
    secondary = secondaryLightHighContrast,
    onSecondary = onSecondaryLightHighContrast,
    secondaryContainer = secondaryContainerLightHighContrast,
    onSecondaryContainer = onSecondaryContainerLightHighContrast,
    tertiary = tertiaryLightHighContrast,
    onTertiary = onTertiaryLightHighContrast,
    tertiaryContainer = tertiaryContainerLightHighContrast,
    onTertiaryContainer = onTertiaryContainerLightHighContrast,
    error = errorLightHighContrast,
    onError = onErrorLightHighContrast,
    errorContainer = errorContainerLightHighContrast,
    onErrorContainer = onErrorContainerLightHighContrast,
    background = backgroundLightHighContrast,
    onBackground = onBackgroundLightHighContrast,
    surface = surfaceLightHighContrast,
    onSurface = onSurfaceLightHighContrast,
    surfaceVariant = surfaceVariantLightHighContrast,
    onSurfaceVariant = onSurfaceVariantLightHighContrast,
    outline = outlineLightHighContrast,
    outlineVariant = outlineVariantLightHighContrast,
    scrim = scrimLightHighContrast,
    inverseSurface = inverseSurfaceLightHighContrast,
    inverseOnSurface = inverseOnSurfaceLightHighContrast,
    inversePrimary = inversePrimaryLightHighContrast,
    surfaceDim = surfaceDimLightHighContrast,
    surfaceBright = surfaceBrightLightHighContrast,
    surfaceContainerLowest = surfaceContainerLowestLightHighContrast,
    surfaceContainerLow = surfaceContainerLowLightHighContrast,
    surfaceContainer = surfaceContainerLightHighContrast,
    surfaceContainerHigh = surfaceContainerHighLightHighContrast,
    surfaceContainerHighest = surfaceContainerHighestLightHighContrast,
)

private val mediumContrastDarkColorScheme = darkColorScheme(
    primary = primaryDarkMediumContrast,
    onPrimary = onPrimaryDarkMediumContrast,
    primaryContainer = primaryContainerDarkMediumContrast,
    onPrimaryContainer = onPrimaryContainerDarkMediumContrast,
    secondary = secondaryDarkMediumContrast,
    onSecondary = onSecondaryDarkMediumContrast,
    secondaryContainer = secondaryContainerDarkMediumContrast,
    onSecondaryContainer = onSecondaryContainerDarkMediumContrast,
    tertiary = tertiaryDarkMediumContrast,
    onTertiary = onTertiaryDarkMediumContrast,
    tertiaryContainer = tertiaryContainerDarkMediumContrast,
    onTertiaryContainer = onTertiaryContainerDarkMediumContrast,
    error = errorDarkMediumContrast,
    onError = onErrorDarkMediumContrast,
    errorContainer = errorContainerDarkMediumContrast,
    onErrorContainer = onErrorContainerDarkMediumContrast,
    background = backgroundDarkMediumContrast,
    onBackground = onBackgroundDarkMediumContrast,
    surface = surfaceDarkMediumContrast,
    onSurface = onSurfaceDarkMediumContrast,
    surfaceVariant = surfaceVariantDarkMediumContrast,
    onSurfaceVariant = onSurfaceVariantDarkMediumContrast,
    outline = outlineDarkMediumContrast,
    outlineVariant = outlineVariantDarkMediumContrast,
    scrim = scrimDarkMediumContrast,
    inverseSurface = inverseSurfaceDarkMediumContrast,
    inverseOnSurface = inverseOnSurfaceDarkMediumContrast,
    inversePrimary = inversePrimaryDarkMediumContrast,
    surfaceDim = surfaceDimDarkMediumContrast,
    surfaceBright = surfaceBrightDarkMediumContrast,
    surfaceContainerLowest = surfaceContainerLowestDarkMediumContrast,
    surfaceContainerLow = surfaceContainerLowDarkMediumContrast,
    surfaceContainer = surfaceContainerDarkMediumContrast,
    surfaceContainerHigh = surfaceContainerHighDarkMediumContrast,
    surfaceContainerHighest = surfaceContainerHighestDarkMediumContrast,
)

private val highContrastDarkColorScheme = darkColorScheme(
    primary = primaryDarkHighContrast,
    onPrimary = onPrimaryDarkHighContrast,
    primaryContainer = primaryContainerDarkHighContrast,
    onPrimaryContainer = onPrimaryContainerDarkHighContrast,
    secondary = secondaryDarkHighContrast,
    onSecondary = onSecondaryDarkHighContrast,
    secondaryContainer = secondaryContainerDarkHighContrast,
    onSecondaryContainer = onSecondaryContainerDarkHighContrast,
    tertiary = tertiaryDarkHighContrast,
    onTertiary = onTertiaryDarkHighContrast,
    tertiaryContainer = tertiaryContainerDarkHighContrast,
    onTertiaryContainer = onTertiaryContainerDarkHighContrast,
    error = errorDarkHighContrast,
    onError = onErrorDarkHighContrast,
    errorContainer = errorContainerDarkHighContrast,
    onErrorContainer = onErrorContainerDarkHighContrast,
    background = backgroundDarkHighContrast,
    onBackground = onBackgroundDarkHighContrast,
    surface = surfaceDarkHighContrast,
    onSurface = onSurfaceDarkHighContrast,
    surfaceVariant = surfaceVariantDarkHighContrast,
    onSurfaceVariant = onSurfaceVariantDarkHighContrast,
    outline = outlineDarkHighContrast,
    outlineVariant = outlineVariantDarkHighContrast,
    scrim = scrimDarkHighContrast,
    inverseSurface = inverseSurfaceDarkHighContrast,
    inverseOnSurface = inverseOnSurfaceDarkHighContrast,
    inversePrimary = inversePrimaryDarkHighContrast,
    surfaceDim = surfaceDimDarkHighContrast,
    surfaceBright = surfaceBrightDarkHighContrast,
    surfaceContainerLowest = surfaceContainerLowestDarkHighContrast,
    surfaceContainerLow = surfaceContainerLowDarkHighContrast,
    surfaceContainer = surfaceContainerDarkHighContrast,
    surfaceContainerHigh = surfaceContainerHighDarkHighContrast,
    surfaceContainerHighest = surfaceContainerHighestDarkHighContrast,
)