[versions]
agp = "8.9.2"
barcodeScanning = "17.3.0"
cameraCore = "1.5.0-beta01"
jlatexmath = "1.2"
kotlin = "2.1.21"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
commons-text = "1.13.1"
kotlinLogging = "7.0.7"
ktor = "3.1.3"
lifecycleRuntimeKtx = "2.9.0"
activityCompose = "1.10.1"
composeBom = "2025.05.01"
navigationCompose = "2.9.0"
markdown = "0.7.3"
okhttp = "4.12.0"
retrofit = "3.0.0"
coil = "3.2.0"
quickjs = "3.2.0"
room = "2.7.1"
koin-bom = "4.0.4"
datastore = "1.1.7"
serialization-json = "1.8.1"
kotlin-coroutines-core = "1.10.2"
kotlinx-datetime = "0.6.2"
ksp = "2.1.21-2.0.1"
paging = "3.3.6"
lucide-icons = "1.1.0"
ucrop = "4.1.0-non-native"
image-viewer = "1.1.0-alpha.7"
google-services = "4.4.2"
firebase-bom = "33.13.0"
firebase-crashlytics = "3.0.3"
jsoup = "1.20.1"
zxing = "3.5.3"
quickie-bundled = "1.10.0"
sonner = "0.3.10"
permissionsCompose = "0.0.1+4"
leakcanary = "2.14"
workmanager = "2.10.1"
reorderable = "2.4.3"
objectbox = "4.3.0"
mcp = "0.5.0"
pebble = "3.2.4"

[libraries]
androidx-camera-core = { module = "androidx.camera:camera-core", version.ref = "cameraCore" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
barcode-scanning = { module = "com.google.mlkit:barcode-scanning", version.ref = "barcodeScanning" }
jlatexmath = { module = "com.github.rikkahub.jlatexmath-android:jlatexmath", version.ref = "jlatexmath" }
jlatexmath-font-greek = { module = "com.github.rikkahub.jlatexmath-android:jlatexmath-font-greek", version.ref = "jlatexmath" }
jlatexmath-font-cyrillic = { module = "com.github.rikkahub.jlatexmath-android:jlatexmath-font-cyrillic", version.ref = "jlatexmath" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3", version="1.4.0-alpha14" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigationCompose" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-paging = { group = "androidx.room", name = "room-paging", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-paging-runtime = { group = "androidx.paging", name = "paging-runtime", version.ref = "paging" }
androidx-paging-compose = { group = "androidx.paging", name = "paging-compose", version.ref = "paging" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
androidx-work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "workmanager" }
jetbrains-markdown = { group = "org.jetbrains", name = "markdown", version.ref = "markdown" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-sse = { group = "com.squareup.okhttp3", name = "okhttp-sse", version.ref = "okhttp" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-serialization-json = { group = "com.squareup.retrofit2", name = "converter-kotlinx-serialization", version.ref = "retrofit" }
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-okhttp = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil"}
coil-svg = { group = "io.coil-kt.coil3", name = "coil-svg", version.ref = "coil" }
quickjs = { group = "wang.harlon.quickjs", name = "wrapper-android", version.ref = "quickjs" }
koin-bom = { group = "io.insert-koin", name = "koin-bom", version.ref = "koin-bom" }
koin-android = { group = "io.insert-koin", name = "koin-android"}
koin-compose = { group = "io.insert-koin", name = "koin-androidx-compose" }
koin-androidx-workmanager = { group = "io.insert-koin", name = "koin-androidx-workmanager" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "serialization-json" }
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "kotlin-coroutines-core" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinx-datetime" }
lucide-icons = { group = "com.composables", name = "icons-lucide", version.ref = "lucide-icons" }
ucrop = { group = "com.github.jens-muenker", name = "uCrop-n-Edit", version.ref = "ucrop" }
image-viewer = { group = "com.jvziyaoyao.scale", name = "image-viewer", version.ref = "image-viewer" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
jsoup = { group = "org.jsoup", name = "jsoup", version.ref = "jsoup" }
zxing-core = { group = "com.google.zxing", name = "core", version.ref = "zxing" }
quickie-bundled = { group = "io.github.g00fy2.quickie", name = "quickie-bundled", version.ref = "quickie-bundled" }
sonner = { module = "io.github.brdominguez:compose-sonner", version.ref = "sonner" }
permissions-compose = { group = "com.meticha", name = "permissions_compose", version.ref = "permissionsCompose" }
leakcanary-android = { module = "com.squareup.leakcanary:leakcanary-android", version.ref = "leakcanary" }
reorderable = { module = "sh.calvin.reorderable:reorderable", version.ref = "reorderable" }
commons-text = { module = "org.apache.commons:commons-text", version.ref = "commons-text" }
modelcontextprotocol-kotlin-sdk = { module = "io.modelcontextprotocol:kotlin-sdk", version.ref = "mcp" }
pebble = { module = "io.pebbletemplates:pebble", version.ref = "pebble" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
google-services = { id = "com.google.gms.google-services", version.ref = "google-services" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebase-crashlytics" }
objectbox = { id = "io.objectbox", version.ref = "objectbox"}