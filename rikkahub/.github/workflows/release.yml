name: Release Build

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
      
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'

    - name: Gradle cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }} 

    - name: Prepare build files
      run: |
        # 解码base64编码的keystore文件
        echo "${{ secrets.KEY_BASE64 }}" | base64 -d > app/app.key
        
        # 写入签名配置到local.properties
        echo "${{ secrets.SIGNING_CONFIG }}" > local.properties
        
        # 写入Google Services配置
        echo '${{ secrets.GOOGLE_SERVICES_JSON }}' > app/google-services.json

    - name: Gradle Build
      run: |
        chmod +x gradlew
        ./gradlew assembleRelease

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      if: success()
      with:
        name: artifacts
        path: app/build/outputs/apk/release/*.apk