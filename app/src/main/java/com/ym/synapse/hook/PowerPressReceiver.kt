package com.ym.synapse.hook

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class PowerPressReceiver : BroadcastReceiver() {

    companion object {
        const val TAG = "PowerPressReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == HookEntry.ACTION_DOUBLE_PRESS_POWER) {
            Log.d(TAG, "Received double power press broadcast!")

            // 标记截图已触发，用于教程界面停止倒计时
            val sharedPreferences = context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
            with(sharedPreferences.edit()) {
                putBoolean("screenshot_triggered", true)
                putLong("screenshot_trigger_time", System.currentTimeMillis())
                apply()
            }

            // Start the background service to handle screenshot logic
            val serviceIntent = Intent(context, ScreenshotService::class.java)
            context.startService(serviceIntent)
            Log.d(TAG, "Starting ScreenshotService.")
        }
    }
}
