package com.ym.synapse.hook

import android.content.Context
import android.content.Intent
import com.ym.synapse.utils.RootUtils
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XC_MethodReplacement
import de.robv.android.xposed.XposedBridge
import de.robv.android.xposed.XposedHelpers
import de.robv.android.xposed.callbacks.XC_LoadPackage

class HookEntry : IXposedHookLoadPackage {

    companion object {
        const val TAG = "SynapseHook"
        // The custom action for our broadcast
        const val ACTION_DOUBLE_PRESS_POWER = "com.ym.synapse.ACTION_DOUBLE_PRESS_POWER"
    }

    override fun handleLoadPackage(lpparam: XC_LoadPackage.LoadPackageParam) {
        XposedBridge.log("$TAG: Attempting to handle package: ${lpparam.packageName}, Target: android") // Enhanced Log

        // Hook for system_server (android package)
        if (lpparam.packageName == "android") {
            hookOplusWalletController(lpparam)
        }

        // Hook for own app package (com.ym.synapse)
        if (lpparam.packageName == "com.ym.synapse") {
            hookKeepAliveServiceDestroy(lpparam)
        }
    }

    private fun hookOplusWalletController(lpparam: XC_LoadPackage.LoadPackageParam) {

        XposedBridge.log("$TAG: Successfully targeted 'android' package. Proceeding with hook for OplusWalletController.") // Enhanced Log

        val walletControllerClass = "com.android.server.policy.OplusWalletController"
        val targetMethodName = "startWallet" // CORRECTED METHOD NAME

        try {
            XposedBridge.log("$TAG: Attempting to find and hook method: $walletControllerClass#$targetMethodName(boolean)") // Enhanced Log with signature

            XposedHelpers.findAndHookMethod(
                walletControllerClass,
                lpparam.classLoader,
                targetMethodName, // CORRECTED METHOD NAME
                Boolean::class.java, // ADDED PARAMETER TYPE for setupComplete
                object : XC_MethodReplacement() {
                    override fun replaceHookedMethod(param: MethodHookParam): Any? {
                        XposedBridge.log("$TAG: $targetMethodName(boolean) intercepted!") // Log with correct method
                        try {
                            // It's good practice to check if thisObject is non-null
                            if (param.thisObject == null) {
                                XposedBridge.log("$TAG: param.thisObject is null in $targetMethodName hook.")
                                return null // Or handle error appropriately
                            }

                            XposedBridge.log("$TAG: Attempting to start ScreenshotService via root.")
                            val serviceComponent = "com.ym.synapse/com.ym.synapse.hook.ScreenshotService" // Ensure this path is correct
                            val rootCommand = "am startservice -n $serviceComponent"
                            
                            // Call RootUtils to execute the command
                            RootUtils.execute(rootCommand) // Assumes RootUtils is imported
                            XposedBridge.log("$TAG: Root command to start service issued: $rootCommand")

                            // Existing broadcast logic (can be kept as a fallback or for other purposes)
                            XposedBridge.log("$TAG: Attempting to send broadcast as fallback.")
                            val context = XposedHelpers.getObjectField(param.thisObject, "mContext") as? android.content.Context
                            if (context == null) {
                                XposedBridge.log("$TAG: Failed to get mContext from ${param.thisObject} for broadcast.")
                            } else {
                                val broadcastIntent = Intent(ACTION_DOUBLE_PRESS_POWER)
                                broadcastIntent.setPackage("com.ym.synapse")
                                broadcastIntent.addFlags(0x01000000) // Intent.FLAG_RECEIVER_INCLUDE_BACKGROUND
                                context.sendBroadcast(broadcastIntent)
                                XposedBridge.log("$TAG: Broadcast $ACTION_DOUBLE_PRESS_POWER sent to com.ym.synapse.")
                            }

                        } catch (hookBodyError: Throwable) { // More specific catch variable
                            XposedBridge.log("$TAG: Error inside $targetMethodName replaceHookedMethod: ${hookBodyError.message}")
                            XposedBridge.log(hookBodyError) // Log the full stack trace
                        }
                        return null // Prevent original method call
                    }
                }
            )
            XposedBridge.log("$TAG: Successfully hooked $walletControllerClass#$targetMethodName(boolean)") // Log with signature
        } catch (hookingError: Throwable) { // More specific catch variable
            XposedBridge.log("$TAG: Critical error during findAndHookMethod for $walletControllerClass#$targetMethodName(boolean): ${hookingError.message}")
            XposedBridge.log(hookingError) // Log the full stack trace
        }
    }

    private fun hookKeepAliveServiceDestroy(lpparam: XC_LoadPackage.LoadPackageParam) {
        val serviceClassName = "com.ym.synapse.hook.KeepAliveService"
        try {
            XposedBridge.log("$TAG: Attempting to find and hook method: $serviceClassName#onDestroy() for package ${lpparam.packageName}")

            XposedHelpers.findAndHookMethod(
                serviceClassName,
                lpparam.classLoader,
                "onDestroy",
                object : XC_MethodHook() {
                    override fun beforeHookedMethod(param: MethodHookParam) {
                        XposedBridge.log("$TAG: $serviceClassName.onDestroy() intercepted. Attempting to restart service.")
                        val context = param.thisObject as? Context
                        if (context != null) {
                            try {
                                val serviceIntent = Intent(context, XposedHelpers.findClass(serviceClassName, lpparam.classLoader)) // Dynamically find class
                                context.startService(serviceIntent)
                                XposedBridge.log("$TAG: Restart intent sent for $serviceClassName")
                            } catch (e: Throwable) {
                                XposedBridge.log("$TAG: Error restarting $serviceClassName in onDestroy hook: ${e.message}")
                                XposedBridge.log(e)
                            }
                        } else {
                            XposedBridge.log("$TAG: Context was null or not a Context in $serviceClassName.onDestroy() hook. Cannot restart service.")
                        }
                    }
                }
            )
            XposedBridge.log("$TAG: Successfully hooked $serviceClassName#onDestroy()")
        } catch (hookingError: Throwable) {
            XposedBridge.log("$TAG: Critical error during findAndHookMethod for $serviceClassName#onDestroy(): ${hookingError.message}")
            XposedBridge.log(hookingError)
        }
    }
}
