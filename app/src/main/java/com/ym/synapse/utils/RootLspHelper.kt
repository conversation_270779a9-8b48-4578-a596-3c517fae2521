package com.ym.synapse.utils

import android.content.Context
import android.content.pm.PackageManager
import java.io.File

/**
 * Root和LSP权限检测工具类
 */
object RootLspHelper {

    /**
     * 检查Root权限
     */
    fun checkRootPermission(): <PERSON><PERSON><PERSON> {
        return try {
            // 方法1: 检查su命令是否存在
            val suPaths = arrayOf(
                "/system/bin/su",
                "/system/xbin/su",
                "/sbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su"
            )
            
            for (path in suPaths) {
                if (File(path).exists()) {
                    return true
                }
            }
            
            // 方法2: 尝试执行su命令
            val process = Runtime.getRuntime().exec("su")
            val exitValue = process.waitFor()
            exitValue == 0
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查LSP框架是否安装
     */
    fun checkLspFramework(context: Context): Boolean {
        return try {
            val packageManager = context.packageManager
            
            // 检查常见的LSP框架
            val lspPackages = arrayOf(
                "de.robv.android.xposed.installer", // Xposed Installer
                "org.meowcat.edxposed.manager",     // EdXposed Manager
                "io.github.lsposed.manager",        // LSPosed Manager
                "com.solohsu.android.edxp.manager", // EdXposed Manager (另一个包名)
                "top.canyie.dreamland.manager"      // Dreamland Manager
            )
            
            for (packageName in lspPackages) {
                try {
                    packageManager.getPackageInfo(packageName, 0)
                    return true
                } catch (e: PackageManager.NameNotFoundException) {
                    // 继续检查下一个
                }
            }
            
            false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查LSP模块是否激活
     */
    fun checkLspModuleActive(): Boolean {
        return try {
            // 检查LSP模块特有的文件或标记
            // 这里需要根据具体的LSP模块实现来检查
            
            // 方法1: 检查Xposed框架的标记文件
            val xposedBridge = File("/system/framework/XposedBridge.jar")
            if (xposedBridge.exists()) {
                return true
            }
            
            // 方法2: 检查LSPosed的标记
            val lsposedBridge = File("/system/framework/lspd")
            if (lsposedBridge.exists()) {
                return true
            }
            
            // 方法3: 尝试加载Xposed API
            try {
                Class.forName("de.robv.android.xposed.XposedBridge")
                return true
            } catch (e: ClassNotFoundException) {
                // Xposed API不可用
            }
            
            false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查当前应用的LSP模块是否启用
     */
    fun checkCurrentAppLspEnabled(context: Context): Boolean {
        return try {
            // 这里需要根据具体的LSP模块实现来检查
            // 通常LSP模块会在应用启动时设置特定的标记或属性
            
            // 方法1: 检查系统属性
            val property = System.getProperty("xposed.bridge.version")
            if (property != null) {
                return true
            }
            
            // 方法2: 检查特定的类是否被Hook
            // 这需要根据具体的Hook实现来判断
            
            // 方法3: 检查SharedPreferences中的标记
            val prefs = context.getSharedPreferences("lsp_status", Context.MODE_PRIVATE)
            prefs.getBoolean("module_active", false)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取Root权限状态描述
     */
    fun getRootStatusDescription(): String {
        return if (checkRootPermission()) {
            "Root权限已获取"
        } else {
            "Root权限未获取"
        }
    }

    /**
     * 获取LSP框架状态描述
     */
    fun getLspFrameworkStatusDescription(context: Context): String {
        return if (checkLspFramework(context)) {
            "LSP框架已安装"
        } else {
            "LSP框架未安装"
        }
    }

    /**
     * 获取LSP模块状态描述
     */
    fun getLspModuleStatusDescription(): String {
        return if (checkLspModuleActive()) {
            "LSP模块已激活"
        } else {
            "LSP模块未激活"
        }
    }

    /**
     * 获取当前应用LSP状态描述
     */
    fun getCurrentAppLspStatusDescription(context: Context): String {
        return if (checkCurrentAppLspEnabled(context)) {
            "当前应用LSP模块已启用"
        } else {
            "当前应用LSP模块未启用"
        }
    }

    /**
     * 获取完整的权限检查结果
     */
    data class PermissionStatus(
        val hasRoot: Boolean,
        val hasLspFramework: Boolean,
        val hasLspModule: Boolean,
        val hasCurrentAppLsp: Boolean,
        val rootDescription: String,
        val lspFrameworkDescription: String,
        val lspModuleDescription: String,
        val currentAppLspDescription: String
    )

    /**
     * 执行完整的权限检查
     */
    fun checkAllPermissions(context: Context): PermissionStatus {
        val hasRoot = checkRootPermission()
        val hasLspFramework = checkLspFramework(context)
        val hasLspModule = checkLspModuleActive()
        val hasCurrentAppLsp = checkCurrentAppLspEnabled(context)
        
        return PermissionStatus(
            hasRoot = hasRoot,
            hasLspFramework = hasLspFramework,
            hasLspModule = hasLspModule,
            hasCurrentAppLsp = hasCurrentAppLsp,
            rootDescription = getRootStatusDescription(),
            lspFrameworkDescription = getLspFrameworkStatusDescription(context),
            lspModuleDescription = getLspModuleStatusDescription(),
            currentAppLspDescription = getCurrentAppLspStatusDescription(context)
        )
    }

    /**
     * 检查是否所有必要权限都已获取
     */
    fun areAllPermissionsGranted(context: Context): Boolean {
        val status = checkAllPermissions(context)
        return status.hasRoot && status.hasLspFramework && status.hasLspModule && status.hasCurrentAppLsp
    }
}
