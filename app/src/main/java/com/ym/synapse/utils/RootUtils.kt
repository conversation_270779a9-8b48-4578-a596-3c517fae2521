package com.ym.synapse.utils

import android.util.Log
import java.io.DataOutputStream
import java.io.IOException

object RootUtils {
    private const val TAG = "RootUtils"

    /**
     * Executes a command as root.
     * Note: This is a simple fire-and-forget implementation.
     */
    fun execute(command: String) {
        var process: Process? = null
        var os: DataOutputStream? = null
        try {
            process = Runtime.getRuntime().exec("su")
            os = DataOutputStream(process.outputStream)
            os.writeBytes("$command\n")
            os.writeBytes("exit\n")
            os.flush()
            val exitCode = process.waitFor()
            Log.d(TAG, "Command '$command' executed with exit code $exitCode")
        } catch (e: IOException) {
            Log.e(TAG, "Error executing root command", e)
        } catch (e: InterruptedException) {
            Log.e(TAG, "Root command interrupted", e)
        } finally {
            try {
                os?.close()
                process?.destroy()
            } catch (e: IOException) {
                Log.e(TAG, "Error closing resources", e)
            }
        }
    }
}
