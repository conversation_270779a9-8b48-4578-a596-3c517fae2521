package com.ym.synapse.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import android.util.Log
import com.ym.synapse.AiOcrApiService
import com.ym.synapse.AiOcrRequest
import com.ym.synapse.ContentPart
import com.ym.synapse.DEFAULT_AI_OCR_MODEL_ID
import com.ym.synapse.DEFAULT_AI_OCR_PROMPT
import com.ym.synapse.ImageUrl
import com.ym.synapse.KEY_AI_OCR_MODEL_ID
import com.ym.synapse.KEY_AI_OCR_PROMPT
import com.ym.synapse.KEY_API_KEY
import com.ym.synapse.KEY_API_URL
import com.ym.synapse.Message
import com.ym.synapse.PREFS_NAME
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.HttpException
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.concurrent.TimeUnit

object AiOcrHelper {

    private const val TAG = "AiOcrHelper"

    private fun getRetrofitClient(apiUrl: String): AiOcrApiService {
        val baseUrl = if (apiUrl.endsWith("/")) apiUrl else "$apiUrl/"

        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        val client = OkHttpClient.Builder()
            .addInterceptor(logging)
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build()

        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(AiOcrApiService::class.java)
    }

    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.NO_WRAP)
    }

    suspend fun classifyImage(context: Context, imagePath: String): String {
        return withContext(Dispatchers.IO) {
            try {
                // 1. Read settings
                val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val apiUrl = sharedPreferences.getString(KEY_API_URL, null)
                val apiKey = sharedPreferences.getString(KEY_API_KEY, null)
                val modelId = sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID

                if (apiUrl.isNullOrEmpty() || apiKey.isNullOrEmpty()) {
                    Log.e(TAG, "API URL or Key is not set.")
                    return@withContext "错误: 未配置API URL或密钥。"
                }

                // 2. Load bitmap and convert to Base64
                val imageFile = File(imagePath)
                if (!imageFile.exists()) {
                    Log.e(TAG, "Image file does not exist at path: $imagePath")
                    return@withContext "错误: 找不到截图文件。"
                }
                val bitmap = BitmapFactory.decodeFile(imagePath)
                if (bitmap == null) {
                    Log.e(TAG, "Failed to decode bitmap from path: $imagePath")
                    return@withContext "错误: 无法读取截图图片。"
                }
                val base64Image = bitmapToBase64(bitmap)
                val imageUrl = "data:image/jpeg;base64,$base64Image"

                // 3. Construct request with the fixed classification prompt
                val request = AiOcrRequest(
                    model = modelId,
                    messages = listOf(
                        Message(
                            role = "user",
                            content = listOf(
                                ContentPart(type = "text", text = AiPrompts.CLASSIFICATION_PROMPT),
                                ContentPart(type = "image_url", image_url = ImageUrl(url = imageUrl))
                            )
                        )
                    ),
                    max_tokens = 50 // Lower max_tokens for classification
                )

                // 4. Make API call
                val service = getRetrofitClient(apiUrl)
                val fullUrl = if (apiUrl.endsWith("/chat/completions")) apiUrl else apiUrl.removeSuffix("/") + "/chat/completions"
                val response = service.recognizeImage(
                    fullUrl = fullUrl,
                    authorization = "Bearer $apiKey",
                    request = request
                )

                // 5. Process response
                val resultText = response.choices?.firstOrNull()?.message?.content?.trim() ?: "未返回任何类别。"
                bitmap.recycle()
                resultText

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                Log.e(TAG, "AI classification failed with HTTP exception: ${e.code()} - $errorBody", e)
                "错误: ${e.code()} - $errorBody"
            } catch (e: Exception) {
                Log.e(TAG, "AI classification failed with an exception.", e)
                "错误: ${e.message}"
            }
        }
    }
}
