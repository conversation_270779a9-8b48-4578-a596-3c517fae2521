package com.ym.synapse

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import android.util.Log
import com.ym.synapse.KEY_API_KEY
import com.ym.synapse.KEY_AI_OCR_MODEL_ID // This is correct
import com.ym.synapse.KEY_API_URL
import com.ym.synapse.PREFS_NAME
import com.ym.synapse.KEY_AI_OCR_PROMPT // Corrected import
import com.ym.synapse.DEFAULT_AI_OCR_PROMPT // Corrected import
import com.ym.synapse.DEFAULT_AI_OCR_MODEL_ID // Import the new default model ID
import com.ym.synapse.R // Ensure R class is imported

// SharedPreferences keys are now in AppConstants.kt

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen() {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    var apiUrl by remember { mutableStateOf(sharedPreferences.getString(KEY_API_URL, "") ?: "") }
    var apiKey by remember { mutableStateOf(sharedPreferences.getString(KEY_API_KEY, "") ?: "") }
    var modelId by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID) }
    var aiOcrPromptState by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_PROMPT, DEFAULT_AI_OCR_PROMPT) ?: DEFAULT_AI_OCR_PROMPT) }

    var showResetDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text("AI OCR Settings", style = MaterialTheme.typography.headlineMedium)

        OutlinedTextField(
            value = apiUrl,
            onValueChange = { apiUrl = it },
            label = { Text("API URL") },
            placeholder = { Text(stringResource(id = R.string.settings_api_url_hint)) },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = apiKey,
            onValueChange = { apiKey = it },
            label = { Text("API Key") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = modelId,
            onValueChange = { modelId = it },
            label = { Text(stringResource(id = R.string.settings_api_model_id_label)) }, // This should now resolve if R is imported
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = aiOcrPromptState, // This should now be resolved
            onValueChange = { aiOcrPromptState = it },
            label = { Text("AI OCR Prompt") }, 
            placeholder = { Text("e.g., Extract text accurately.") }, 
            modifier = Modifier.fillMaxWidth(),
            singleLine = false,
            maxLines = 5
        )

        Button(
            onClick = {
                Log.d("SettingsScreen", "Saving API URL: $apiUrl, API Key: $apiKey, Model ID: $modelId, AI OCR Prompt: $aiOcrPromptState")
                with(sharedPreferences.edit()) {
                    putString(KEY_API_URL, apiUrl)
                    putString(KEY_API_KEY, apiKey)
                    putString(KEY_AI_OCR_MODEL_ID, modelId)
                    putString(KEY_AI_OCR_PROMPT, aiOcrPromptState)
                    apply()
                }
                // Optionally, show a Toast or Snackbar
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Save Settings")
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 重新初始化按钮
        OutlinedButton(
            onClick = {
                showResetDialog = true
            },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.error
            )
        ) {
            Text("🔄 重新初始化")
        }

        Text(
            text = "重新初始化将清除所有配置，重新开始设置流程",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }

    // 确认重新初始化对话框
    if (showResetDialog) {
        AlertDialog(
            onDismissRequest = { showResetDialog = false },
            title = { Text("确认重新初始化") },
            text = {
                Text("这将清除所有配置和设置，应用将重新开始初始化流程。\n\n确定要继续吗？")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showResetDialog = false
                        Log.d("SettingsScreen", "Resetting app to initial state")

                        // 清除所有配置状态，重新开始初始化流程
                        val appStatePrefs = context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
                        with(appStatePrefs.edit()) {
                            clear() // 清除所有app_state数据
                            apply()
                        }

                        // 重启应用到初始状态
                        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
                        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                        context.startActivity(intent)

                        // 结束当前Activity
                        if (context is android.app.Activity) {
                            context.finish()
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("确认重新初始化")
                }
            },
            dismissButton = {
                TextButton(onClick = { showResetDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    // Wrap in a theme if your app uses one, for accurate preview
    // SynapseTheme { // Assuming you have a theme
        SettingsScreen()
    // }
}
