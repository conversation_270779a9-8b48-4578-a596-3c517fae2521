<resources>
    <string name="app_name">synapse</string>
    <string name="ocr_title">OCR Recognition</string>
    <string name="ocr_button_local">Local OCR</string>
    <string name="ocr_button_ai">AI OCR</string>
    <string name="ocr_ai_coming_soon">AI OCR coming soon!</string>
    <string name="ocr_results_placeholder">Recognition results will appear here.</string>
    <string name="ocr_image_selected_processing">Image selected, processing...</string>
    <string name="ocr_load_image_failed">Failed to load image.</string>
    <string name="ocr_no_image_selected">No image selected.</string>
    <string name="ocr_select_image_prompt">Please select an image from your gallery.</string>
    <string name="ocr_no_text_found">No text found in image.</string>
    <string name="ocr_recognition_failed">Recognition failed: %s</string>
    <string name="ocr_image_preview_desc">Selected Image Preview</string>
    <string name="ocr_error_api_config_missing">AI OCR 失败：API 地址或 Key 未配置。请检查设置。</string>
    <string name="ocr_ai_processing">正在进行 AI OCR 识别...</string>
    <string name="settings_api_model_id_label">AI OCR Model ID</string>
    <string name="settings_api_url_hint">例如: https://your-api-server.com/v1/chat/completions</string>
    <string name="ocr_no_text_found_ai">AI OCR 未识别到文本。</string>
    <string name="ocr_error_ai_failed">AI OCR 识别失败：%1$s</string>
    <string name="ocr_error_network">网络请求失败：%1$s</string>
    <string name="ocr_prompt_select_image_for_ai">请先选择一张图片再进行 AI 识别</string>

    <!-- 权限相关字符串 -->
    <string name="permission_title">权限检测</string>
    <string name="permission_overview_all_granted">所有权限已授予</string>
    <string name="permission_overview_need_grant">需要授予权限</string>
    <string name="permission_overview_status">已授予 %1$d/%2$d 项权限</string>
    <string name="permission_details_title">权限详情</string>
    <string name="permission_refresh_status">刷新状态</string>
    <string name="permission_request_all">请求权限</string>
    <string name="permission_request_this">请求此权限</string>
    <string name="permission_status_granted">已授予</string>
    <string name="permission_status_not_granted">未授予</string>

    <!-- 具体权限名称和描述 -->
    <string name="permission_notification_name">通知权限</string>
    <string name="permission_notification_desc">用于显示截图处理结果和状态通知</string>
    <string name="permission_media_name">相册读取权限</string>
    <string name="permission_media_desc">用于读取和处理截图文件</string>
    <string name="permission_storage_name">文件读取权限</string>
    <string name="permission_storage_desc">用于访问设备存储中的文件</string>
</resources>